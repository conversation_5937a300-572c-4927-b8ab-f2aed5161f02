import { PrismaClient } from './generated/client'

const prisma = new PrismaClient()

async function testFinalRawSQL() {
  try {
    console.log('🧪 Testing final raw SQL currency fix...')

    // Clean up any existing test data
    await prisma.currencyRate.deleteMany({
      where: {
        source: {
          startsWith: 'test'
        }
      }
    })

    const dailyCurrencyService = (await import('./server/services/dailyCurrencyRate.service')).default

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    console.log('\n1. Creating first rate...')
    const result1 = await dailyCurrencyService.createManualRate({
      fromCurrency: 'USD',
      toCurrency: 'IDR',
      rate: 15500,
      sellRate: 15810,
      buyRate: 15190,
      margin: 0.02,
      source: 'test_final_1',
      date: today
    })

    console.log('Result 1:', result1.status ? '✅ Success' : `❌ Failed: ${result1.message}`)

    console.log('\n2. Updating same rate...')
    const result2 = await dailyCurrencyService.createManualRate({
      fromCurrency: 'USD',
      toCurrency: 'IDR',
      rate: 15600,
      sellRate: 15912,
      buyRate: 15288,
      margin: 0.02,
      source: 'test_final_2',
      date: today
    })

    console.log('Result 2:', result2.status ? '✅ Success' : `❌ Failed: ${result2.message}`)

    // Check records
    const records = await prisma.currencyRate.findMany({
      where: {
        fromCurrency: 'USD',
        toCurrency: 'IDR',
        date: today
      }
    })

    console.log(`\n3. Records found: ${records.length}`)
    if (records.length === 1) {
      console.log('✅ Only one record exists (correct)')
      console.log(`Final rate: ${records[0].rate} (source: ${records[0].source})`)
    } else if (records.length === 0) {
      console.log('❌ No records found')
    } else {
      console.log('❌ Multiple records found')
      records.forEach((r, i) => {
        console.log(`Record ${i + 1}: rate=${r.rate}, source=${r.source}`)
      })
    }

    // Test currency conversion
    console.log('\n4. Testing currency conversion...')
    const currencyService = (await import('./server/services/currency.service')).default
    const converted = await currencyService.convertCurrency(4500, 'USD', 'IDR')
    console.log(`$4,500 USD = Rp${converted.toLocaleString('id-ID')} IDR`)

    if (converted > 60000000 && converted < 80000000) {
      console.log('✅ Currency conversion is correct!')
    } else {
      console.log('❌ Currency conversion seems wrong')
    }

    // Cleanup
    await prisma.currencyRate.deleteMany({
      where: {
        source: {
          startsWith: 'test'
        }
      }
    })
    console.log('\n✅ Cleaned up test data')

    console.log('\n🎉 Final test completed successfully!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testFinalRawSQL()
