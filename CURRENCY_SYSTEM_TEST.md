# 🧪 Currency System Testing Guide

## ✅ New Currency System Architecture

### Backend (Database Caching)
1. **First Load**: Check database for today's exchange rate
2. **If Not Found**: Fetch from external API and save to database
3. **If Found**: Use cached rate from database
4. **Frontend**: Get rate once and use for all conversions

### Frontend (Global State Management)
1. **Single API Call**: Fetch exchange rate once on app load
2. **Local Conversion**: All price conversions use cached rate
3. **Currency Switch**: Only invalidate queries, no new API calls
4. **Efficient**: No repeated API calls for same rate

## 🔧 Testing Steps

### 1. Test Currency Loading
```bash
# Check if exchange rate is fetched and cached
curl -X GET http://localhost:3000/currency/rate
```

Expected Response:
```json
{
  "status": true,
  "message": "Exchange rate retrieved successfully",
  "data": {
    "rate": 15539.25,
    "lastUpdated": "2024-01-20T10:00:00.000Z",
    "source": "database"
  }
}
```

### 2. Test Currency Switching in Frontend
1. Open browser dev tools
2. Go to marketplace page
3. Switch currency from USD to IDR
4. Check console logs:
   - Should see: "🔄 Switching currency from USD to IDR"
   - Should see: "✅ Currency switched to IDR"
   - Prices should update immediately

### 3. Test Product Price Display
1. Check product cards show prices
2. Switch currency and verify prices change
3. Verify format is correct:
   - USD: $100.00
   - IDR: Rp 1,553,925

### 4. Test Auto-bid Functionality
1. Go to auction product detail page
2. Enable auto-bid with:
   - Max Budget: $500
   - Bid Increment: $10
3. Check auto-bid status shows:
   - Current Bid: $100
   - Next Auto-bid: $110
   - Bid Increment: $10
   - Max Budget: $500
   - Remaining Budget: $400

### 5. Test Bid History
1. Go to auction product detail page
2. Click "View Bid History"
3. Should show list of bids with proper formatting

## 🐛 Common Issues & Solutions

### Issue: Prices not showing
**Solution**: Check if currency service is loaded
```javascript
// In browser console
console.log(window.__CURRENCY_SERVICE__)
```

### Issue: Currency not switching
**Solution**: Check query invalidation
```javascript
// Should see invalidation logs in console
```

### Issue: Auto-bid not working
**Solution**: Check backend logs for auto-bid processing

### Issue: Exchange rate not updating
**Solution**: Check database for today's rate
```sql
SELECT * FROM ExchangeRate WHERE DATE(createdAt) = CURDATE();
```

## 📊 Performance Metrics

### Before (Old System)
- Multiple API calls per page load
- Repeated currency conversions
- No caching mechanism
- Slow currency switching

### After (New System)
- Single API call per day
- Cached conversions
- Database caching
- Instant currency switching

## 🎯 Expected Behavior

1. **First Visit**: Fetch rate from API, save to DB, cache in frontend
2. **Subsequent Visits**: Use cached rate from DB
3. **Currency Switch**: Instant conversion using cached rate
4. **Auto-bid**: Real-time updates with proper currency formatting
5. **Bid History**: Proper display with currency conversion

## 🔍 Debug Commands

### Check Exchange Rate in Database
```sql
SELECT * FROM ExchangeRate ORDER BY createdAt DESC LIMIT 5;
```

### Check Auto-bid Status
```sql
SELECT * FROM AutoBid WHERE isActive = true;
```

### Check Product Current Bids
```sql
SELECT id, itemName, currentBid, bidCount FROM Product WHERE sellType = 'auction';
```

### Frontend Debug
```javascript
// Check currency service state
console.log(useCurrencyService());

// Check currency context
console.log(useCurrencyLanguage());
```
