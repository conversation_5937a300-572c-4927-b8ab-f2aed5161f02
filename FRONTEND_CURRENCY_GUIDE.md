# 🎯 Frontend Currency System - Backend Integration Guide

## 🚀 Sistem Baru: No More External API Calls!

Semua external API calls di frontend telah dihapus dan diganti dengan sistem yang efisien menggunakan backend API.

---

## ✅ **Yang Sudah Diimplementasikan**

### **1. Enhanced Currency Query Service**
```typescript
// src/services/useCurrencyQuery.ts

// ✅ Get exchange rate dari backend (auto-fetch jika belum ada)
useExchangeRateQuery(fromCurrency, toCurrency)

// ✅ Convert currency menggunakan backend API
useCurrencyConversion(amount, fromCurrency, toCurrency)

// ✅ Get products dengan harga terkonversi
useProductsWithCurrencyQuery(currency, params)

// ✅ Initialize exchange rates (setup)
useInitializeExchangeRatesMutation()

// ✅ Refresh exchange rate (force update)
useRefreshExchangeRateMutation()
```

### **2. Currency State Management Hook**
```typescript
// src/hooks/useCurrencyState.ts

const {
  currentCurrency,           // Current selected currency
  exchangeRate,             // Current exchange rate from backend
  isLoading,                // Loading state
  lastUpdated,              // Last update timestamp
  switchCurrency,           // Switch currency + fetch rate
  refreshExchangeRate,      // Force refresh rate
  convertPrice,             // Convert price using current rate
  formatPrice,              // Format price with currency
  isReady                   // Ready to use
} = useCurrencyState('IDR')
```

### **3. Enhanced Currency Context**
```typescript
// src/contexts/CurrencyLanguageContext.tsx

const {
  currency,                 // Current currency
  setCurrency,              // Switch currency (async)
  exchangeRate,             // Backend exchange rate
  isLoadingRate,            // Rate loading state
  refreshExchangeRate,      // Refresh rate function
  formatPrice,              // Format with backend rate
  convertPrice,             // Convert with backend rate
  isReady                   // System ready
} = useCurrencyLanguage()
```

### **4. Cleaned Currency Utils**
```typescript
// src/utils/currency.ts

// ⚠️ DEPRECATED functions (use backend instead):
convertCurrencyFallback()   // Emergency fallback only
getFallbackExchangeRates()  // Emergency fallback only

// ✅ Still useful:
formatPrice()               // Format price with currency symbol
getCurrencySymbol()         // Get currency symbol
```

---

## 🔄 **How It Works**

### **Flow Ketika User Switch Currency:**
```
1. User click currency switcher
2. Frontend call setCurrency(newCurrency)
3. Save to localStorage
4. Call backend: GET /product-pricing/rate?from=USD&to=IDR
5. Backend check database untuk rate hari ini
6. Jika tidak ada → Backend hit external API + save to DB
7. Return rate ke frontend
8. Frontend update state dan invalidate product queries
9. Products re-fetch dengan currency baru
10. UI update dengan harga terkonversi
```

### **Product Pricing Flow:**
```
1. Frontend request: GET /products/with-pricing?currency=IDR
2. Backend get products dari database
3. Untuk setiap product:
   - Check original currency vs target currency
   - Jika beda → convert menggunakan exchange rate dari DB
   - Return product dengan originalPrice + convertedPrice
4. Frontend display harga terkonversi
```

---

## 🧪 **Testing Guide**

### **1. Setup Exchange Rates**
```bash
# Initialize exchange rates di backend
curl -X POST http://localhost:3000/product-pricing/initialize
```

### **2. Test Currency Switching di Frontend**
```typescript
// Di component manapun:
const { setCurrency, currency, exchangeRate, isLoading } = useCurrencyLanguage()

// Switch ke IDR
await setCurrency('IDR')

// Switch ke USD  
await setCurrency('USD')

// Check current rate
console.log(`Current rate: 1 USD = ${exchangeRate} ${currency}`)
```

### **3. Test Product Pricing**
```typescript
// Get products dengan harga terkonversi
const { data: productsData } = useProductsWithCurrencyQuery('IDR', {
  page: 1,
  limit: 10
})

// Products akan punya:
// - originalPrice: 100 (USD)
// - convertedPrice: 1553925 (IDR)
// - exchangeRate: 15539.25
```

### **4. Test Manual Conversion**
```typescript
const { data: conversionData } = useCurrencyConversion(100, 'USD', 'IDR')

// Result:
// {
//   originalAmount: 100,
//   convertedAmount: 1553925,
//   exchangeRate: 15539.25,
//   source: 'database'
// }
```

---

## 📱 **Frontend Integration Examples**

### **Product Card Component**
```typescript
const ProductCard = ({ product }) => {
  const { currency, formatPrice, convertPrice } = useCurrencyLanguage()
  
  // Harga akan otomatis terkonversi sesuai currency yang dipilih
  const displayPrice = product.convertedPrice || product.originalPrice
  
  return (
    <div>
      <h3>{product.itemName}</h3>
      <p>{formatPrice(displayPrice)}</p>
      {product.priceSource === 'converted' && (
        <small>
          Original: {formatPrice(product.originalPrice, product.originalCurrency)}
        </small>
      )}
    </div>
  )
}
```

### **Currency Switcher Component**
```typescript
const CurrencySwitcher = () => {
  const { currency, setCurrency, isLoadingRate, exchangeRate } = useCurrencyLanguage()
  
  const handleCurrencyChange = async (newCurrency) => {
    await setCurrency(newCurrency)
  }
  
  return (
    <div>
      <select value={currency} onChange={(e) => handleCurrencyChange(e.target.value)}>
        <option value="USD">USD ($)</option>
        <option value="IDR">IDR (Rp)</option>
      </select>
      
      {isLoadingRate && <span>Loading rate...</span>}
      
      <small>
        Rate: 1 USD = {exchangeRate.toLocaleString()} IDR
      </small>
    </div>
  )
}
```

### **Product List with Pricing**
```typescript
const ProductList = () => {
  const { currency } = useCurrencyLanguage()
  const { data: productsData, isLoading } = useProductsWithCurrencyQuery(currency, {
    page: 1,
    limit: 20
  })
  
  if (isLoading) return <div>Loading products...</div>
  
  return (
    <div>
      <h2>Products in {currency}</h2>
      {productsData?.products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  )
}
```

---

## 🔧 **Advanced Usage**

### **Force Refresh Exchange Rate**
```typescript
const { refreshExchangeRate, isLoadingRate } = useCurrencyLanguage()

const handleRefreshRate = async () => {
  await refreshExchangeRate()
  console.log('Exchange rate refreshed!')
}
```

### **Currency State Hook (Advanced)**
```typescript
const {
  currentCurrency,
  exchangeRate,
  switchCurrency,
  getExchangeRateInfo,
  convertAndFormatPrice,
  isStale,
  isReady
} = useCurrencyState('IDR')

// Get detailed rate info
const rateInfo = getExchangeRateInfo()
console.log(rateInfo.formatted) // "1 USD = 15,539 IDR"

// Check if rate is stale (fallback)
if (isStale) {
  console.warn('Using fallback rate - refresh recommended')
}
```

### **Products with Currency Hook**
```typescript
const { currency } = useProductsWithCurrency('IDR', { category: 'electronics' })

// Automatically invalidates and refetches when currency changes
```

---

## ⚡ **Performance Benefits**

### **Before (External API Calls):**
- ❌ Multiple external API calls dari frontend
- ❌ Rate limiting issues
- ❌ Slow response times
- ❌ No caching
- ❌ Inconsistent rates

### **After (Backend Integration):**
- ✅ Single API call ke backend
- ✅ Database caching (5 minutes)
- ✅ Fast response times
- ✅ Consistent rates across app
- ✅ Auto-fallback system
- ✅ Efficient state management

---

## 🛡️ **Error Handling**

### **Automatic Fallbacks:**
```
Backend API → Database Cache → Static Fallback → Error
```

### **Loading States:**
```typescript
const { isLoadingRate, isReady } = useCurrencyLanguage()

if (!isReady) {
  return <div>Setting up currency system...</div>
}

if (isLoadingRate) {
  return <div>Updating exchange rates...</div>
}
```

### **Error Recovery:**
```typescript
// System automatically retries and falls back
// No manual error handling needed in most cases
```

---

## 🎯 **Migration Checklist**

- [x] ✅ Remove all external API calls from frontend
- [x] ✅ Update useCurrencyQuery service
- [x] ✅ Create useCurrencyState hook
- [x] ✅ Update CurrencyLanguageContext
- [x] ✅ Clean currency utils
- [x] ✅ Add backend integration
- [x] ✅ Add proper error handling
- [x] ✅ Add loading states
- [x] ✅ Add caching strategy

---

## 🚀 **Ready to Use!**

Sistem currency frontend sekarang:
- ✅ **100% Backend Integrated** - No external API calls
- ✅ **Efficient State Management** - Smart caching dan invalidation
- ✅ **Auto Currency Switch** - Seamless currency switching
- ✅ **Real-time Pricing** - Products update otomatis
- ✅ **Error Proof** - Multiple fallback layers
- ✅ **Performance Optimized** - Fast response times

**Tinggal pakai hooks dan context yang sudah ada - semuanya otomatis!** 🎉
