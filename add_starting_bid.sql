-- Add startingBid field to AutoBid table
ALTER TABLE `AutoBid` ADD COLUMN `startingBid` DECIMAL(10,2) NOT NULL DEFAULT 0.00;

-- Update existing records to have a reasonable starting bid
-- Set startingBid to current bid + bid increment for existing auto-bids
UPDATE `AutoBid` ab
JOIN `Product` p ON ab.productId = p.id
SET ab.startingBid = COALESCE(p.currentBid, p.priceUSD) + ab.bidIncrement
WHERE ab.startingBid = 0.00;
