import { prisma } from "../db";
import { errorResponse } from "../utils/response.util";
import {
  CreateInvoiceInput,
  CreateEWalletChargeInput,
  WebhookPayload,
} from "../schemas/payment.schema";
import crypto from "crypto";

// Xendit configuration with enhanced security
const XENDIT_SECRET_KEY = process.env.XENDIT_SECRET_KEY;
const XENDIT_WEBHOOK_TOKEN = process.env.XENDIT_WEBHOOK_TOKEN;
const XENDIT_BASE_URL = process.env.XENDIT_BASE_URL || "https://api.xendit.co";
const XENDIT_ENVIRONMENT = process.env.XENDIT_ENVIRONMENT || "sandbox"; // sandbox or production

// Enhanced security validation
if (!XENDIT_SECRET_KEY) {
  throw new Error("XENDIT_SECRET_KEY environment variable is required");
}

if (!XENDIT_WEBHOOK_TOKEN) {
  console.warn(
    "XENDIT_WEBHOOK_TOKEN not set - webhook signature verification will be skipped"
  );
}

// Validate environment
if (!["sandbox", "production"].includes(XENDIT_ENVIRONMENT)) {
  throw new Error(
    "XENDIT_ENVIRONMENT must be either 'sandbox' or 'production'"
  );
}

class PaymentService {
  private getXenditHeaders() {
    return {
      Authorization: `Basic ${Buffer.from(XENDIT_SECRET_KEY + ":").toString("base64")}`,
      "Content-Type": "application/json",
      "User-Agent": `KingCollectibles/1.0 (${XENDIT_ENVIRONMENT})`,
      "X-API-VERSION": "2020-02-01",
    };
  }

  /**
   * Enhanced security: Generate secure external ID with timestamp and random component
   */
  private generateSecureExternalId(orderId: string): string {
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString("hex");
    return `order_${orderId}_${timestamp}_${random}`;
  }

  /**
   * Create Xendit invoice
   */
  async createInvoice(data: CreateInvoiceInput) {
    try {
      // Get order details
      const order = await prisma.order.findUnique({
        where: { id: data.orderId },
        include: {
          user: true,
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId: data.orderId },
      });

      if (existingPayment) {
        if (existingPayment.status === "PAID") {
          return errorResponse("Order already paid");
        }

        // If payment exists but not paid, delete it to create a new one
        if (existingPayment.status === "PENDING" || existingPayment.status === "EXPIRED" || existingPayment.status === "FAILED") {
          await prisma.payment.delete({
            where: { id: existingPayment.id }
          });
        }
      }

      // Generate secure external ID
      const externalId = this.generateSecureExternalId(data.orderId);

      // Convert amount based on currency
      let amount = Number(order.total);
      const currency = data.currency || order.currency || "USD";

      // Import currency service for real-time conversion
      const currencyService = (await import('./currency.service')).default;

      // Convert currency if needed
      if (order.currency !== currency) {
        try {
          amount = await currencyService.convertCurrency(
            amount,
            order.currency as any,
            currency as any
          );
        } catch (error) {
          console.error('Currency conversion failed, using fallback rates:', error);
          // Fallback to static rates
          if (order.currency === "USD" && currency === "IDR") {
            amount = amount * 15000;
          } else if (order.currency === "IDR" && currency === "USD") {
            amount = amount / 15000;
          }
        }
      }

      // Prepare invoice data
      const invoiceData = {
        external_id: externalId,
        amount: Math.round(amount),
        currency: "IDR",
        customer: {
          given_names: data.customerName,
          email: data.customerEmail,
        },
        description:
          data.description || `Payment for Order #${order.orderNumber}`,
        invoice_duration: 86400, // 24 hours
        success_redirect_url: data.successRedirectUrl,
        failure_redirect_url: data.failureRedirectUrl,
        payment_methods: this.getPaymentMethodsForCurrency(currency),
      };

      // Create invoice with Xendit
      const response = await fetch(`${XENDIT_BASE_URL}/v2/invoices`, {
        method: "POST",
        headers: this.getXenditHeaders(),
        body: JSON.stringify(invoiceData),
      });

      console.error("Xendit invoice creation response:", response);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Xendit invoice creation failed:", errorData);
        return {
          status: false,
          message: "Failed to create payment invoice",
          error: errorData,
        };
      }

      const xenditInvoice = await response.json();
      console.log("Xendit invoice created:", xenditInvoice);

      // Save payment record
      const payment = await prisma.payment.create({
        data: {
          orderId: data.orderId,
          xenditInvoiceId: xenditInvoice.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          invoiceUrl: xenditInvoice.invoice_url,
          expiryDate: new Date(xenditInvoice.expiry_date),
        },
      });

      return {
        status: true,
        message: "Invoice created successfully",
        data: {
          id: payment.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          invoiceId: xenditInvoice.id,
          invoiceUrl: xenditInvoice.invoice_url || "",
          expiryDate: xenditInvoice.expiry_date || "",
          paymentMethods: xenditInvoice.available_banks || [],
          availableEwallets: xenditInvoice.available_ewallets || [],
          availableRetailOutlets: xenditInvoice.available_retail_outlets || [],
        },
      };
    } catch (error) {
      console.error("Create invoice error:", error);
      return {
        status: false,
        message: "Failed to create invoice",
      };
    }
  }

  /**
   * Create eWallet charge
   */
  async createEWalletCharge(data: CreateEWalletChargeInput) {
    try {
      // Get order details
      const order = await prisma.order.findUnique({
        where: { id: data.orderId },
      });

      if (!order) {
        return {
          status: false,
          message: "Order not found",
        };
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId: data.orderId },
      });

      if (existingPayment) {
        if (existingPayment.status === "PAID") {
          return {
            status: false,
            message: "Order already paid",
          };
        }

        // If payment exists but not paid, delete it to create a new one
        if (existingPayment.status === "PENDING" || existingPayment.status === "EXPIRED" || existingPayment.status === "FAILED") {
          await prisma.payment.delete({
            where: { id: existingPayment.id }
          });
        }
      }

      // Generate secure external ID
      const externalId = this.generateSecureExternalId(data.orderId);

      // Convert amount based on currency
      let amount = Number(order.total);
      const currency = data.currency || order.currency || "USD";

      // Import currency service for real-time conversion
      const currencyService = (await import('./currency.service')).default;

      // Convert currency if needed
      if (order.currency !== currency) {
        try {
          amount = await currencyService.convertCurrency(
            amount,
            order.currency as any,
            currency as any
          );
        } catch (error) {
          console.error('Currency conversion failed, using fallback rates:', error);
          // Fallback to static rates
          if (order.currency === "USD" && currency === "IDR") {
            amount = amount * 15000;
          } else if (order.currency === "IDR" && currency === "USD") {
            amount = amount / 15000;
          }
        }
      }

      // Prepare eWallet charge data
      const chargeData = {
        reference_id: externalId,
        currency: currency,
        amount: Math.round(amount),
        checkout_method: "ONE_TIME_PAYMENT",
        channel_code: data.ewalletType,
        channel_properties: {
          mobile_number: data.customerPhone,
        },
        customer: {
          given_names: data.customerName,
          mobile_number: data.customerPhone,
        },
      };

      // Create eWallet charge with Xendit
      const response = await fetch(`${XENDIT_BASE_URL}/ewallets/charges`, {
        method: "POST",
        headers: this.getXenditHeaders(),
        body: JSON.stringify(chargeData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Xendit eWallet charge creation failed:", errorData);
        return {
          status: false,
          message: "Failed to create eWallet charge",
          error: errorData,
        };
      }

      const xenditCharge = await response.json();

      // Save payment record
      const payment = await prisma.payment.create({
        data: {
          orderId: data.orderId,
          xenditPaymentId: xenditCharge.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          paymentMethod: "ewallet",
          paymentChannel: data.ewalletType,
        },
      });

      return {
        status: true,
        message: "eWallet charge created successfully",
        data: {
          id: payment.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          chargeId: xenditCharge.id,
          actions: xenditCharge.actions,
        },
      };
    } catch (error) {
      console.error("Create eWallet charge error:", error);
      return {
        status: false,
        message: "Failed to create eWallet charge",
      };
    }
  }

  /**
   * Get payment details from Xendit
   */
  async getPaymentDetails(paymentId: string) {
    try {
      const payment = await prisma.payment.findUnique({
        where: { id: paymentId },
        include: {
          order: {
            include: {
              items: {
                include: {
                  product: {
                    include: { images: true },
                  },
                },
              },
              shippingAddress: true,
            },
          },
        },
      });

      if (!payment) {
        return errorResponse("Payment not found");
      }

      // Get invoice details from Xendit if it's an invoice payment
      let xenditDetails = null;
      if (payment.xenditInvoiceId) {
        try {
          const response = await fetch(
            `${XENDIT_BASE_URL}/v2/invoices/${payment.xenditInvoiceId}`,
            {
              method: "GET",
              headers: this.getXenditHeaders(),
            }
          );

          if (response.ok) {
            xenditDetails = await response.json();
          }
        } catch (error) {
          console.error("Failed to fetch Xendit invoice details:", error);
        }
      }

      // Get eWallet charge details if it's an eWallet payment
      if (payment.xenditPaymentId && payment.paymentMethod === "ewallet") {
        try {
          const response = await fetch(
            `${XENDIT_BASE_URL}/ewallets/charges/${payment.xenditPaymentId}`,
            {
              method: "GET",
              headers: this.getXenditHeaders(),
            }
          );

          if (response.ok) {
            xenditDetails = await response.json();
          }
        } catch (error) {
          console.error("Failed to fetch Xendit eWallet details:", error);
        }
      }

      return {
        status: true,
        data: {
          payment,
          xenditDetails,
          paymentInstructions: this.generatePaymentInstructions(
            payment,
            xenditDetails
          ),
        },
      };
    } catch (error) {
      console.error("Get payment details error:", error);
      return errorResponse("Failed to get payment details");
    }
  }

  /**
   * Generate payment instructions based on payment method
   */
  private generatePaymentInstructions(payment: any, xenditDetails: any) {
    const instructions: any = {
      method: payment.paymentMethod,
      status: payment.status,
      amount: payment.amount,
      currency: payment.currency,
    };

    if (xenditDetails) {
      switch (payment.paymentMethod) {
        case "xendit_invoice":
          instructions.invoiceUrl = xenditDetails.invoice_url;
          instructions.expiryDate = xenditDetails.expiry_date;
          instructions.availableChannels = xenditDetails.available_banks || [];
          instructions.availableRetailOutlets =
            xenditDetails.available_retail_outlets || [];
          instructions.availableEwallets =
            xenditDetails.available_ewallets || [];
          break;

        case "bank_transfer":
          if (
            xenditDetails.available_banks &&
            xenditDetails.available_banks.length > 0
          ) {
            instructions.bankCode = xenditDetails.available_banks[0].bank_code;
            instructions.accountNumber =
              xenditDetails.available_banks[0].account_number;
            instructions.bankName = xenditDetails.available_banks[0].bank_name;
          }
          break;

        case "ewallet":
          instructions.ewalletType = payment.paymentMethod;
          instructions.actions = xenditDetails?.actions;
          instructions.qrString = xenditDetails?.actions?.qr_checkout_string;
          instructions.mobileDeeplink =
            xenditDetails?.actions?.mobile_deeplink_checkout_url;
          break;

        case "credit_card":
          instructions.cardNumber = xenditDetails.masked_card_number;
          instructions.cardType = xenditDetails.card_type;
          break;
      }
    }

    return instructions;
  }

  /**
   * Create Virtual Account payment
   */
  async createVirtualAccount(data: any) {
    try {
      const { orderId, bankCode, customerName, currency = "IDR" } = data;

      // Get order details
      const order = await prisma.order.findUnique({
        where: { id: orderId },
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId },
      });

      if (existingPayment) {
        if (existingPayment.status === "PAID") {
          return errorResponse("Order already paid");
        }

        // If payment exists but not paid, delete it to create a new one
        if (existingPayment.status === "PENDING" || existingPayment.status === "EXPIRED" || existingPayment.status === "FAILED") {
          await prisma.payment.delete({
            where: { id: existingPayment.id }
          });
        }
      }

      const amount = Number(order.total);
      const externalId = this.generateSecureExternalId(orderId);

      // Create Xendit Virtual Account
      const response = await fetch(
        `${XENDIT_BASE_URL}/callback_virtual_accounts`,
        {
          method: "POST",
          headers: this.getXenditHeaders(),
          body: JSON.stringify({
            external_id: externalId,
            bank_code: bankCode,
            name: customerName,
            expected_amount: amount,
            is_closed: true,
            expiration_date: new Date(
              Date.now() + 24 * 60 * 60 * 1000
            ).toISOString(), // 24 hours
          }),
        }
      );

      console.log("response", response)

      if (!response.ok) {
        const errorData = await response.json();
        return {
          status: false,
          message: "Failed to create virtual account",
          error: errorData,
        };
      }

      const xenditVA = await response.json();

      // Save payment record
      const payment = await prisma.payment.create({
        data: {
          orderId: orderId,
          xenditPaymentId: xenditVA.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          paymentMethod: "virtual_account",
          paymentChannel: bankCode,
        },
      });

      return {
        status: true,
        message: "Virtual Account created successfully",
        data: {
          id: payment.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          bankCode: bankCode,
          accountNumber: xenditVA.account_number,
          bankName: xenditVA.bank_code,
        },
      };
    } catch (error) {
      console.error("Create Virtual Account error:", error);
      return errorResponse("Failed to create virtual account");
    }
  }

  /**
   * Create Retail Outlet payment
   */
  async createRetailOutlet(data: any) {
    try {
      const {
        orderId,
        retailOutletName,
        customerName,
        currency = "IDR",
      } = data;

      // Get order details
      const order = await prisma.order.findUnique({
        where: { id: orderId },
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId },
      });

      if (existingPayment) {
        if (existingPayment.status === "PAID") {
          return errorResponse("Order already paid");
        }

        // If payment exists but not paid, delete it to create a new one
        if (existingPayment.status === "PENDING" || existingPayment.status === "EXPIRED" || existingPayment.status === "FAILED") {
          await prisma.payment.delete({
            where: { id: existingPayment.id }
          });
        }
      }

      const amount = Number(order.total);
      const externalId = this.generateSecureExternalId(orderId);

      // Create Xendit Retail Outlet
      const response = await fetch(`${XENDIT_BASE_URL}/fixed_payment_code`, {
        method: "POST",
        headers: this.getXenditHeaders(),
        body: JSON.stringify({
          external_id: externalId,
          retail_outlet_name: retailOutletName,
          name: customerName,
          expected_amount: amount,
          expiration_date: new Date(
            Date.now() + 24 * 60 * 60 * 1000
          ).toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          status: false,
          message: "Failed to create retail outlet payment",
          error: errorData,
        };
      }

      const xenditRetail = await response.json();

      // Save payment record
      const payment = await prisma.payment.create({
        data: {
          orderId: orderId,
          xenditPaymentId: xenditRetail.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          paymentMethod: "retail_outlet",
          paymentChannel: retailOutletName,
        },
      });

      return {
        status: true,
        message: "Retail Outlet payment created successfully",
        data: {
          id: payment.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          retailOutletName: retailOutletName,
          paymentCode: xenditRetail.payment_code,
        },
      };
    } catch (error) {
      console.error("Create Retail Outlet error:", error);
      return errorResponse("Failed to create retail outlet payment");
    }
  }

  /**
   * Create QR Code payment
   */
  async createQRCode(data: any) {
    try {
      const { orderId, qrCodeType, currency = "IDR" } = data;

      // Get order details
      const order = await prisma.order.findUnique({
        where: { id: orderId },
      });

      if (!order) {
        return errorResponse("Order not found");
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId },
      });

      if (existingPayment) {
        if (existingPayment.status === "PAID") {
          return errorResponse("Order already paid");
        }

        // If payment exists but not paid, delete it to create a new one
        if (existingPayment.status === "PENDING" || existingPayment.status === "EXPIRED" || existingPayment.status === "FAILED") {
          await prisma.payment.delete({
            where: { id: existingPayment.id }
          });
        }
      }

      const amount = Number(order.total);
      const externalId = this.generateSecureExternalId(orderId);

      // Create Xendit QR Code
      const response = await fetch(`${XENDIT_BASE_URL}/qr_codes`, {
        method: "POST",
        headers: this.getXenditHeaders(),
        body: JSON.stringify({
          external_id: externalId,
          type: "DYNAMIC",
          callback_url: `${process.env.NEXT_PUBLIC_API_URL}/payments/webhook`,
          amount: amount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          status: false,
          message: "Failed to create QR code payment",
          error: errorData,
        };
      }

      const xenditQR = await response.json();

      // Save payment record
      const payment = await prisma.payment.create({
        data: {
          orderId: orderId,
          xenditPaymentId: xenditQR.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: "IDR",
          paymentMethod: "qr_code",
          paymentChannel: qrCodeType,
        },
      });

      return {
        status: true,
        message: "QR Code payment created successfully",
        data: {
          id: payment.id,
          externalId: externalId,
          status: "PENDING",
          amount: amount,
          currency: currency,
          qrCodeType: qrCodeType,
          qrString: xenditQR.qr_string,
        },
      };
    } catch (error) {
      console.error("Create QR Code error:", error);
      return errorResponse("Failed to create QR code payment");
    }
  }

  /**
   * Handle Xendit webhook
   */
  async handleWebhook(payload: WebhookPayload, signature: string) {
    try {
      // Verify webhook signature
      if (
        XENDIT_WEBHOOK_TOKEN &&
        !this.verifyWebhookSignature(payload, signature)
      ) {
        throw new Error("Invalid webhook signature");
      }

      // Find payment by external ID
      const payment = await prisma.payment.findUnique({
        where: { externalId: payload.external_id },
        include: { order: true },
      });

      if (!payment) {
        console.warn("Payment not found for external ID:", payload.external_id);
        return { processed: false, reason: "Payment not found" };
      }

      // Update payment status based on webhook
      const updateData: any = {
        webhookData: payload,
        updatedAt: new Date(),
      };

      // Map Xendit status to our status
      switch (payload.status.toUpperCase()) {
        case "PAID":
          updateData.status = "PAID";
          updateData.paidAt = payload.paid_at
            ? new Date(payload.paid_at)
            : new Date();
          updateData.paymentMethod = payload.payment_method;
          updateData.paymentChannel = payload.payment_channel;
          break;
        case "EXPIRED":
          updateData.status = "EXPIRED";
          break;
        case "FAILED":
          updateData.status = "FAILED";
          updateData.failureReason = payload.description;
          break;
        default:
          updateData.status = "PENDING";
      }

      // Update payment
      await prisma.payment.update({
        where: { id: payment.id },
        data: updateData,
      });

      // Update order status if payment is successful
      if (updateData.status === "PAID") {
        await prisma.order.update({
          where: { id: payment.orderId },
          data: {
            paymentStatus: "paid",
            status: "processing",
          },
        });
      }

      return { processed: true, status: updateData.status };
    } catch (error) {
      console.error("Webhook handling error:", error);
      throw error;
    }
  }

  /**
   * Enhanced security: Verify webhook signature with timing-safe comparison
   */
  private verifyWebhookSignature(payload: any, signature: string): boolean {
    if (!XENDIT_WEBHOOK_TOKEN) {
      console.warn(
        "Webhook signature verification skipped - XENDIT_WEBHOOK_TOKEN not set"
      );
      return true;
    }

    return signature === XENDIT_WEBHOOK_TOKEN;
  }

  /**
   * Get payment methods for currency
   */
  getPaymentMethods(currency: "IDR" | "USD") {
    if (currency === "IDR") {
      return {
        invoice: {
          id: "xendit_invoice",
          name: "All Payment Methods",
          description: "Semua metode pembayaran tersedia",
          type: "invoice",
          recommended: true,
        },
        virtualAccount: [
          {
            id: "BCA",
            name: "BCA Virtual Account",
            description: "Transfer via ATM/Mobile Banking BCA",
            type: "virtual_account",
          },
          {
            id: "BNI",
            name: "BNI Virtual Account",
            description: "Transfer via ATM/Mobile Banking BNI",
            type: "virtual_account",
          },
          {
            id: "BRI",
            name: "BRI Virtual Account",
            description: "Transfer via ATM/Mobile Banking BRI",
            type: "virtual_account",
          },
          {
            id: "MANDIRI",
            name: "Mandiri Virtual Account",
            description: "Transfer via ATM/Mobile Banking Mandiri",
            type: "virtual_account",
          },
          {
            id: "PERMATA",
            name: "Permata Virtual Account",
            description: "Transfer via ATM/Mobile Banking Permata",
            type: "virtual_account",
          },
          {
            id: "CIMB",
            name: "CIMB Niaga Virtual Account",
            description: "Transfer via ATM/Mobile Banking CIMB",
            type: "virtual_account",
          },
          {
            id: "DANAMON",
            name: "Danamon Virtual Account",
            description: "Transfer via ATM/Mobile Banking Danamon",
            type: "virtual_account",
          },
          {
            id: "BSI",
            name: "BSI Virtual Account",
            description: "Transfer via ATM/Mobile Banking BSI",
            type: "virtual_account",
          },
        ],
        ewallet: [
          {
            id: "OVO",
            name: "OVO",
            description: "Bayar dengan OVO",
            type: "ewallet",
          },
          {
            id: "DANA",
            name: "DANA",
            description: "Bayar dengan DANA",
            type: "ewallet",
          },
          {
            id: "LINKAJA",
            name: "LinkAja",
            description: "Bayar dengan LinkAja",
            type: "ewallet",
          },
          {
            id: "SHOPEEPAY",
            name: "ShopeePay",
            description: "Bayar dengan ShopeePay",
            type: "ewallet",
          },
          {
            id: "GOPAY",
            name: "GoPay",
            description: "Bayar dengan GoPay",
            type: "ewallet",
          },
          {
            id: "ASTRAPAY",
            name: "AstraPay",
            description: "Bayar dengan AstraPay",
            type: "ewallet",
          },
          {
            id: "JENIUSPAY",
            name: "Jenius Pay",
            description: "Bayar dengan Jenius Pay",
            type: "ewallet",
          },
        ],
        qrCode: [
          {
            id: "QRIS",
            name: "QRIS",
            description: "Scan QR dengan aplikasi bank/e-wallet",
            type: "qr_code",
          },
        ],
        retailOutlet: [
          {
            id: "ALFAMART",
            name: "Alfamart",
            description: "Bayar di Alfamart terdekat",
            type: "retail_outlet",
          },
          {
            id: "INDOMARET",
            name: "Indomaret",
            description: "Bayar di Indomaret terdekat",
            type: "retail_outlet",
          },
          {
            id: "LAWSON",
            name: "Lawson",
            description: "Bayar di Lawson terdekat",
            type: "retail_outlet",
          },
          {
            id: "DAN_DAN",
            name: "Dan+Dan",
            description: "Bayar di Dan+Dan terdekat",
            type: "retail_outlet",
          },
        ],
      };
    } else {
      return {
        invoice: {
          id: "xendit_invoice",
          name: "All Payment Methods",
          description: "Credit Card, Bank Transfer, E-Wallet & More",
          type: "invoice",
          recommended: true,
        },
        creditCard: [
          {
            id: "CREDIT_CARD",
            name: "Credit Card",
            description: "Visa, Mastercard, JCB, AMEX",
            type: "credit_card",
          },
        ],
      };
    }
  }

  /**
   * Get payment methods for Xendit invoice creation
   */
  private getPaymentMethodsForCurrency(currency: string) {
    if (currency === "IDR") {
      return [
        "CREDIT_CARD",
        "BCA",
        "BNI",
        "BRI",
        "MANDIRI",
        "PERMATA",
        "OVO",
        "DANA",
        "LINKAJA",
        "SHOPEEPAY",
        "ALFAMART",
        "INDOMARET",
      ];
    } else {
      return ["CREDIT_CARD"];
    }
  }
}

const paymentService = new PaymentService();
export default paymentService;
