import { errorResponse, successResponse } from "../utils/response.util";

/**
 * Simple currency rate service for development/fallback
 * Uses static rates when database is not available
 */
class SimpleCurrencyRateService {
  private readonly FALLBACK_RATES = {
    USD_TO_IDR: 15000,
    IDR_TO_USD: 1 / 15000,
  } as const;

  private readonly DEFAULT_MARGIN = 0.02; // 2% margin

  /**
   * Get current rates (fallback to static rates)
   */
  async getCurrentRates() {
    try {
      const rates = [
        {
          id: 'fallback-usd-idr',
          fromCurrency: 'USD',
          toCurrency: 'IDR',
          rate: this.FALLBACK_RATES.USD_TO_IDR,
          sellRate: this.FALLBACK_RATES.USD_TO_IDR * (1 + this.DEFAULT_MARGIN),
          buyRate: this.FALLBACK_RATES.USD_TO_IDR * (1 - this.DEFAULT_MARGIN),
          margin: this.DEFAULT_MARGIN,
          source: 'fallback',
          isActive: true,
          date: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'fallback-idr-usd',
          fromCurrency: 'IDR',
          toCurrency: 'USD',
          rate: this.FALLBACK_RATES.IDR_TO_USD,
          sellRate: this.FALLBACK_RATES.IDR_TO_USD * (1 + this.DEFAULT_MARGIN),
          buyRate: this.FALLBACK_RATES.IDR_TO_USD * (1 - this.DEFAULT_MARGIN),
          margin: this.DEFAULT_MARGIN,
          source: 'fallback',
          isActive: true,
          date: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      return successResponse("Fallback currency rates retrieved", {
        rates,
        isToday: false,
        isFallback: true
      });

    } catch (error) {
      console.error('Get fallback rates error:', error);
      return errorResponse("Failed to get fallback rates");
    }
  }

  /**
   * Convert currency using fallback rates
   */
  async convertCurrency(amount: number, fromCurrency: string, toCurrency: string, type: 'buy' | 'sell' = 'sell') {
    try {
      if (fromCurrency === toCurrency) {
        return successResponse("Currency conversion completed", {
          originalAmount: amount,
          convertedAmount: amount,
          rate: 1,
          type,
          isFallback: true
        });
      }

      let rate = 1;
      
      if (fromCurrency === 'USD' && toCurrency === 'IDR') {
        rate = type === 'sell' 
          ? this.FALLBACK_RATES.USD_TO_IDR * (1 + this.DEFAULT_MARGIN)
          : this.FALLBACK_RATES.USD_TO_IDR * (1 - this.DEFAULT_MARGIN);
      } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
        rate = type === 'sell'
          ? this.FALLBACK_RATES.IDR_TO_USD * (1 + this.DEFAULT_MARGIN)
          : this.FALLBACK_RATES.IDR_TO_USD * (1 - this.DEFAULT_MARGIN);
      } else {
        return errorResponse("Currency pair not supported in fallback mode");
      }

      const convertedAmount = amount * rate;

      return successResponse("Currency conversion completed (fallback)", {
        originalAmount: amount,
        convertedAmount,
        rate,
        type,
        isFallback: true,
        rateDate: new Date()
      });

    } catch (error) {
      console.error('Convert currency fallback error:', error);
      return errorResponse("Failed to convert currency using fallback rates");
    }
  }

  /**
   * Update rates (no-op for fallback service)
   */
  async updateDailyRates() {
    console.log('📝 Fallback currency service - no database update needed');
    
    return successResponse("Fallback rates are static - no update needed", {
      usdToIdr: this.FALLBACK_RATES.USD_TO_IDR,
      idrToUsd: this.FALLBACK_RATES.IDR_TO_USD,
      source: 'fallback',
      date: new Date().toISOString(),
      isFallback: true
    });
  }

  /**
   * Get rate history (returns empty for fallback)
   */
  async getRateHistory(fromCurrency: string, toCurrency: string, days: number = 30) {
    return successResponse("Rate history not available in fallback mode", {
      rates: [],
      period: {
        from: new Date(),
        to: new Date(),
        days
      },
      isFallback: true
    });
  }

  /**
   * Test API connectivity
   */
  async testApiConnectivity() {
    const results = {
      exchangeRateApi: false,
      fixerApi: false,
      currencyApi: false
    };

    try {
      // Test ExchangeRate-API
      const exchangeResponse = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      results.exchangeRateApi = exchangeResponse.ok;
    } catch (error) {
      console.log('ExchangeRate-API not accessible:', error.message);
    }

    try {
      // Test Fixer.io (if API key available)
      const fixerApiKey = process.env.FIXER_API_KEY;
      if (fixerApiKey) {
        const fixerResponse = await fetch(`https://api.fixer.io/latest?access_key=${fixerApiKey}&base=USD&symbols=IDR`);
        results.fixerApi = fixerResponse.ok;
      }
    } catch (error) {
      console.log('Fixer.io API not accessible:', error.message);
    }

    try {
      // Test CurrencyAPI (if API key available)
      const currencyApiKey = process.env.CURRENCY_API_KEY;
      if (currencyApiKey) {
        const currencyResponse = await fetch(`https://api.currencyapi.com/v3/latest?apikey=${currencyApiKey}&base_currency=USD&currencies=IDR`);
        results.currencyApi = currencyResponse.ok;
      }
    } catch (error) {
      console.log('CurrencyAPI not accessible:', error.message);
    }

    return successResponse("API connectivity test completed", {
      results,
      availableApis: Object.entries(results).filter(([_, available]) => available).map(([api, _]) => api),
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get live rate from external API (for testing)
   */
  async getLiveRate() {
    try {
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.rates && data.rates.IDR) {
        const liveRate = data.rates.IDR;
        const sellRate = liveRate * (1 + this.DEFAULT_MARGIN);
        const buyRate = liveRate * (1 - this.DEFAULT_MARGIN);

        return successResponse("Live rate retrieved successfully", {
          liveRate,
          sellRate,
          buyRate,
          margin: this.DEFAULT_MARGIN,
          source: 'exchangerate-api.com',
          timestamp: new Date().toISOString(),
          comparison: {
            fallbackRate: this.FALLBACK_RATES.USD_TO_IDR,
            difference: liveRate - this.FALLBACK_RATES.USD_TO_IDR,
            percentageDiff: ((liveRate - this.FALLBACK_RATES.USD_TO_IDR) / this.FALLBACK_RATES.USD_TO_IDR) * 100
          }
        });
      }

      return errorResponse("No IDR rate found in API response");

    } catch (error) {
      console.error('Get live rate error:', error);
      return errorResponse("Failed to get live rate from external API");
    }
  }
}

export default new SimpleCurrencyRateService();
