import { PrismaClient } from "../../generated/client";
import { errorResponse, successResponse } from "../utils/response.util";

// Create a dedicated Prisma instance for product pricing
const prisma = new PrismaClient({
  log: ['error', 'warn'],
  errorFormat: 'pretty',
});

interface ProductPriceConversion {
  productId: string;
  originalPrice: number;
  originalCurrency: string;
  targetCurrency: string;
  convertedPrice: number;
  exchangeRate: number;
  margin?: number;
}

class ProductPricingService {
  private readonly DEFAULT_MARGIN = 0.02; // 2% margin

  /**
   * Get or fetch exchange rate for product pricing
   */
  async getExchangeRateForPricing(fromCurrency: string, toCurrency: string) {
    try {
      if (fromCurrency === toCurrency) {
        return { rate: 1, source: 'same_currency' };
      }

      // Check if we have today's rate in database
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const existingRate = await prisma.currencyRate.findFirst({
        where: {
          fromCurrency,
          toCurrency,
          date: today,
          isActive: true
        }
      });

      if (existingRate) {
        console.log(`✅ Using existing rate: ${fromCurrency}/${toCurrency} = ${existingRate.sellRate}`);
        return {
          rate: Number(existingRate.sellRate),
          source: 'database',
          date: existingRate.date
        };
      }

      // If no rate exists, fetch from API and save
      console.log(`📡 Fetching new rate: ${fromCurrency}/${toCurrency}`);
      const fetchedRate = await this.fetchAndSaveExchangeRate(fromCurrency, toCurrency);
      
      if (fetchedRate) {
        return {
          rate: fetchedRate.sellRate,
          source: 'api_fresh',
          date: today
        };
      }

      // Fallback to static rate
      return this.getFallbackRate(fromCurrency, toCurrency);

    } catch (error) {
      console.error('Get exchange rate error:', error);
      return this.getFallbackRate(fromCurrency, toCurrency);
    }
  }

  /**
   * Fetch exchange rate from API and save to database
   */
  private async fetchAndSaveExchangeRate(fromCurrency: string, toCurrency: string) {
    try {
      // Only support USD-IDR conversion for now
      if (!((fromCurrency === 'USD' && toCurrency === 'IDR') || (fromCurrency === 'IDR' && toCurrency === 'USD'))) {
        console.log(`Unsupported currency pair: ${fromCurrency}/${toCurrency}`);
        return null;
      }

      // Fetch USD to IDR rate from API
      const usdToIdrRate = await this.fetchLiveUsdToIdrRate();
      if (!usdToIdrRate) {
        console.error('Failed to fetch live USD/IDR rate');
        return null;
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Calculate rates with margin
      const usdToIdrSellRate = usdToIdrRate * (1 + this.DEFAULT_MARGIN);
      const usdToIdrBuyRate = usdToIdrRate * (1 - this.DEFAULT_MARGIN);
      
      const idrToUsdRate = 1 / usdToIdrRate;
      const idrToUsdSellRate = idrToUsdRate * (1 + this.DEFAULT_MARGIN);
      const idrToUsdBuyRate = idrToUsdRate * (1 - this.DEFAULT_MARGIN);

      // Save USD to IDR rate
      const existingUsdToIdr = await prisma.currencyRate.findUnique({
        where: {
          fromCurrency_toCurrency_date: {
            fromCurrency: 'USD',
            toCurrency: 'IDR',
            date: today
          }
        }
      });

      // Use upsert to avoid unique constraint error
      const usdToIdrRecord = await prisma.currencyRate.upsert({
        where: {
          fromCurrency_toCurrency_date: {
            fromCurrency: 'USD',
            toCurrency: 'IDR',
            date: today
          }
        },
        update: {
          rate: usdToIdrRate,
          sellRate: usdToIdrSellRate,
          buyRate: usdToIdrBuyRate,
          margin: this.DEFAULT_MARGIN,
          source: 'product_pricing_api',
          isActive: true,
          updatedAt: new Date()
        },
        create: {
          fromCurrency: 'USD',
          toCurrency: 'IDR',
          rate: usdToIdrRate,
          sellRate: usdToIdrSellRate,
          buyRate: usdToIdrBuyRate,
          margin: this.DEFAULT_MARGIN,
          source: 'product_pricing_api',
          date: today,
          isActive: true
        }
      });

      // Save IDR to USD rate using upsert
      await prisma.currencyRate.upsert({
        where: {
          fromCurrency_toCurrency_date: {
            fromCurrency: 'IDR',
            toCurrency: 'USD',
            date: today
          }
        },
        update: {
          rate: idrToUsdRate,
          sellRate: idrToUsdSellRate,
          buyRate: idrToUsdBuyRate,
          margin: this.DEFAULT_MARGIN,
          source: 'product_pricing_api',
          isActive: true,
          updatedAt: new Date()
        },
        create: {
          fromCurrency: 'IDR',
          toCurrency: 'USD',
          rate: idrToUsdRate,
          sellRate: idrToUsdSellRate,
          buyRate: idrToUsdBuyRate,
          margin: this.DEFAULT_MARGIN,
          source: 'product_pricing_api',
          date: today,
          isActive: true
        }
      });

      console.log(`✅ Saved exchange rates: USD/IDR = ${usdToIdrRate} (sell: ${usdToIdrSellRate})`);

      // Return the requested rate
      if (fromCurrency === 'USD' && toCurrency === 'IDR') {
        return {
          rate: usdToIdrRate,
          sellRate: usdToIdrSellRate,
          buyRate: usdToIdrBuyRate
        };
      } else {
        return {
          rate: idrToUsdRate,
          sellRate: idrToUsdSellRate,
          buyRate: idrToUsdBuyRate
        };
      }

    } catch (error) {
      console.error('Fetch and save exchange rate error:', error);
      return null;
    }
  }

  /**
   * Fetch live USD to IDR rate from external API
   */
  private async fetchLiveUsdToIdrRate(): Promise<number | null> {
    try {
      console.log('🌐 Fetching live USD/IDR rate from API...');
      
      // Try ExchangeRate-API first (free, reliable)
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      
      if (response.ok) {
        const data = await response.json();
        if (data.rates && data.rates.IDR && !isNaN(data.rates.IDR)) {
          console.log(`📈 Live rate fetched: 1 USD = ${data.rates.IDR} IDR`);
          return data.rates.IDR;
        }
      }

      // Try alternative API if first fails
      const altResponse = await fetch('https://open.er-api.com/v6/latest/USD');
      if (altResponse.ok) {
        const altData = await altResponse.json();
        if (altData.rates && altData.rates.IDR && !isNaN(altData.rates.IDR)) {
          console.log(`📈 Live rate fetched (alt): 1 USD = ${altData.rates.IDR} IDR`);
          return altData.rates.IDR;
        }
      }

      console.error('❌ All exchange rate APIs failed');
      return null;

    } catch (error) {
      console.error('Fetch live rate error:', error);
      return null;
    }
  }

  /**
   * Get fallback rate for unsupported pairs or API failures
   */
  private getFallbackRate(fromCurrency: string, toCurrency: string) {
    const FALLBACK_RATES = {
      USD_TO_IDR: 15000,
      IDR_TO_USD: 1 / 15000,
    };

    let rate = 1;
    
    if (fromCurrency === 'USD' && toCurrency === 'IDR') {
      rate = FALLBACK_RATES.USD_TO_IDR * (1 + this.DEFAULT_MARGIN);
    } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
      rate = FALLBACK_RATES.IDR_TO_USD * (1 + this.DEFAULT_MARGIN);
    }

    console.log(`⚠️ Using fallback rate: ${fromCurrency}/${toCurrency} = ${rate}`);
    
    return {
      rate,
      source: 'fallback',
      date: new Date()
    };
  }

  /**
   * Convert product price with exchange rate
   */
  async convertProductPrice(price: number, fromCurrency: string, toCurrency: string): Promise<ProductPriceConversion> {
    try {
      // Validate inputs
      if (!price || isNaN(price) || price <= 0) {
        throw new Error('Invalid price provided');
      }

      if (!fromCurrency || !toCurrency) {
        throw new Error('Currency codes are required');
      }

      // Get exchange rate
      const rateInfo = await this.getExchangeRateForPricing(fromCurrency, toCurrency);
      
      if (!rateInfo.rate || isNaN(rateInfo.rate) || rateInfo.rate <= 0) {
        throw new Error('Invalid exchange rate');
      }

      // Calculate converted price
      const convertedPrice = price * rateInfo.rate;
      
      if (isNaN(convertedPrice)) {
        throw new Error('Price conversion resulted in NaN');
      }

      return {
        productId: '', // Will be set by caller
        originalPrice: price,
        originalCurrency: fromCurrency,
        targetCurrency: toCurrency,
        convertedPrice: Math.round(convertedPrice * 100) / 100, // Round to 2 decimal places
        exchangeRate: Math.round(rateInfo.rate * 10000) / 10000, // Round to 4 decimal places
        margin: this.DEFAULT_MARGIN
      };

    } catch (error) {
      console.error('Convert product price error:', error);
      throw error;
    }
  }

  /**
   * Get products with converted prices
   */
  async getProductsWithConvertedPrices(targetCurrency: string = 'IDR', page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;

      const products = await prisma.product.findMany({
        include: {
          images: {
            take: 1,
            orderBy: { sortOrder: 'asc' }
          },
          category: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      });

      const productsWithConvertedPrices = await Promise.all(
        products.map(async (product) => {
          try {
            // Convert price if different currency
            let convertedPrice = Number(product.price);
            let exchangeRate = 1;
            
            if (product.currency !== targetCurrency) {
              const conversion = await this.convertProductPrice(
                Number(product.price),
                product.currency,
                targetCurrency
              );
              convertedPrice = conversion.convertedPrice;
              exchangeRate = conversion.exchangeRate;
            }

            return {
              ...product,
              originalPrice: Number(product.price),
              originalCurrency: product.currency,
              convertedPrice,
              displayCurrency: targetCurrency,
              exchangeRate,
              price: convertedPrice // Override price with converted price
            };
          } catch (error) {
            console.error(`Error converting price for product ${product.id}:`, error);
            // Return original product if conversion fails
            return {
              ...product,
              originalPrice: Number(product.price),
              originalCurrency: product.currency,
              convertedPrice: Number(product.price),
              displayCurrency: product.currency,
              exchangeRate: 1
            };
          }
        })
      );

      return successResponse("Products with converted prices retrieved successfully", {
        products: productsWithConvertedPrices,
        pagination: {
          page,
          limit,
          total: await prisma.product.count()
        },
        targetCurrency
      });

    } catch (error) {
      console.error('Get products with converted prices error:', error);
      return errorResponse("Failed to get products with converted prices");
    }
  }

  /**
   * Initialize exchange rates (run this once to setup)
   */
  async initializeExchangeRates() {
    try {
      console.log('🚀 Initializing exchange rates...');
      
      // Fetch and save USD/IDR rates
      const result = await this.fetchAndSaveExchangeRate('USD', 'IDR');
      
      if (result) {
        return successResponse("Exchange rates initialized successfully", {
          usdToIdr: result.sellRate,
          source: 'api',
          date: new Date().toISOString()
        });
      } else {
        // Create fallback rates
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const fallbackRate = 15000;
        const sellRate = fallbackRate * (1 + this.DEFAULT_MARGIN);
        const buyRate = fallbackRate * (1 - this.DEFAULT_MARGIN);

        const existingFallback = await prisma.currencyRate.findUnique({
          where: {
            fromCurrency_toCurrency_date: {
              fromCurrency: 'USD',
              toCurrency: 'IDR',
              date: today
            }
          }
        });

        // Use upsert for fallback rate
        await prisma.currencyRate.upsert({
          where: {
            fromCurrency_toCurrency_date: {
              fromCurrency: 'USD',
              toCurrency: 'IDR',
              date: today
            }
          },
          update: {
            rate: fallbackRate,
            sellRate,
            buyRate,
            margin: this.DEFAULT_MARGIN,
            source: 'fallback_init',
            isActive: true,
            updatedAt: new Date()
          },
          create: {
            fromCurrency: 'USD',
            toCurrency: 'IDR',
            rate: fallbackRate,
            sellRate,
            buyRate,
            margin: this.DEFAULT_MARGIN,
            source: 'fallback_init',
            date: today,
            isActive: true
          }
        });

        return successResponse("Exchange rates initialized with fallback", {
          usdToIdr: sellRate,
          source: 'fallback',
          date: today.toISOString()
        });
      }

    } catch (error) {
      console.error('Initialize exchange rates error:', error);
      return errorResponse("Failed to initialize exchange rates");
    }
  }
}

const productPricingService = new ProductPricingService();
export default productPricingService;
