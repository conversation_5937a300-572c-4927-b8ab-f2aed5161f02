import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import { 
  createProductSchema, 
  updateProductSchema, 
  productQuerySchema,
  categorySchema,
  itemTypeSchema,
  bidSchema
} from "../schemas/product.schema";
import productController from "../controllers/product.controller";
import { authMiddleware } from "../middlewares/auth";

const productRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Create Product Route
const createProductRoute = createRoute({
  method: "post",
  path: "/",
  request: {
    body: {
      content: {
        "application/json": {
          schema: createProductSchema.openapi("CreateProductSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Product created successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Get Products Route
const getProductsRoute = createRoute({
  method: "get",
  path: "/",
  request: {
    query: productQuerySchema,
  },
  responses: {
    200: {
      description: "Products retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Get Product by ID Route
const getProductByIdRoute = createRoute({
  method: "get",
  path: "/{id}",
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      description: "Product retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    404: {
      description: "Product not found",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Get Product by Slug Route
const getProductBySlugRoute = createRoute({
  method: "get",
  path: "/slug/{slug}",
  request: {
    params: z.object({
      slug: z.string().min(1),
    }),
  },
  responses: {
    200: {
      description: "Product retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    404: {
      description: "Product not found",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Update Product Route
const updateProductRoute = createRoute({
  method: "put",
  path: "/{id}",
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
    body: {
      content: {
        "application/json": {
          schema: updateProductSchema.openapi("UpdateProductSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Product updated successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Delete Product Route
const deleteProductRoute = createRoute({
  method: "delete",
  path: "/{id}",
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      description: "Product deleted successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Get Categories Route
const getCategoriesRoute = createRoute({
  method: "get",
  path: "/categories",
  responses: {
    200: {
      description: "Categories retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Create Category Route
const createCategoryRoute = createRoute({
  method: "post",
  path: "/categories",
  request: {
    body: {
      content: {
        "application/json": {
          schema: categorySchema.openapi("CategorySchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Category created successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Get Item Types Route
const getItemTypesRoute = createRoute({
  method: "get",
  path: "/item-types",
  request: {
    query: z.object({
      categoryId: z.string().uuid().optional(),
    }),
  },
  responses: {
    200: {
      description: "Item types retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Create Item Type Route
const createItemTypeRoute = createRoute({
  method: "post",
  path: "/item-types",
  request: {
    body: {
      content: {
        "application/json": {
          schema: itemTypeSchema.openapi("ItemTypeSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Item type created successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
    500: {
      description: "Internal server error",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any().optional(),
            error: z.any().optional(),
          }),
        }
      }
    },
  },
});

// Place Bid Route
const placeBidRoute = createRoute({
  method: "post",
  path: "/:id/bids",
  request: {
    body: {
      content: {
        "application/json": {
          schema: bidSchema.openapi("BidSchema"),
        },
      },
      required: true,
    },
  },
  responses: {
    201: {
      description: "Bid placed successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        }
      }
    },
    400: {
      description: "Bad request",
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Upload Images Route
const uploadImagesRoute = createRoute({
  method: "post",
  path: "/upload-images",
  responses: {
    200: {
      description: "Images uploaded successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.object({
              urls: z.array(z.string()),
            }),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
  },
});

const bidsProductRoute = createRoute({
  method: "get",
  path: "/:id/bids/list",
  request: {
    params: z.object({
      id: z.string().uuid("Invalid product ID"),
    }),
  },
  responses: {
    200: {
      description: "Bids retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.array(z.object({
              id: z.string().uuid("Invalid bid ID"),
              productId: z.string().uuid("Invalid product ID"),
              userId: z.string().uuid("Invalid user ID"),
              amount: z.number().positive("Bid amount must be positive"),
              createdAt: z.date(),
            })),
          }),
        }
      }
    },
    404: {
      description: "Product not found",
    },
  },
});

const userBidsRoute = createRoute({
  method: "get",
  path: "/:id/user-bid",
  responses: {
    200: {
      description: "User bids retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.array(z.object({
              productId: z.string().uuid("Invalid product ID"),
              highestBid: z.number().positive("Highest bid must be positive"),
              totalBids: z.number().int().nonnegative("Total bids must be non-negative"),
              isWinning: z.boolean(),
              lastBidTime: z.date(),
            })),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized",
    },
  },
});

// Public routes
productRoutes.openapi(getProductsRoute, productController.getProducts);
productRoutes.openapi(getProductByIdRoute, productController.getProductById);
productRoutes.openapi(getProductBySlugRoute, productController.getProductBySlug);
productRoutes.openapi(getCategoriesRoute, productController.getCategories);
productRoutes.openapi(getItemTypesRoute, productController.getItemTypes);

// Products with pricing (currency conversion)
productRoutes.get('/with-pricing', productController.getProductsWithPricing);

// Protected routes (require authentication)
productRoutes.use('/upload-images', authMiddleware);
productRoutes.openapi(uploadImagesRoute, productController.uploadImages);

productRoutes.use('/', authMiddleware);
productRoutes.openapi(createProductRoute, productController.createProduct);

productRoutes.use('/:id', authMiddleware);
productRoutes.openapi(updateProductRoute, productController.updateProduct);
productRoutes.openapi(deleteProductRoute, productController.deleteProduct);

productRoutes.use('/categories', authMiddleware);
productRoutes.openapi(createCategoryRoute, productController.createCategory);

productRoutes.use('/item-types', authMiddleware);
productRoutes.openapi(createItemTypeRoute, productController.createItemType);

productRoutes.use('/:id/bids', authMiddleware);
productRoutes.openapi(placeBidRoute, productController.placeBid);

productRoutes.openapi(bidsProductRoute, productController.getBidsByProductId);

productRoutes.use(userBidsRoute.path, authMiddleware);
productRoutes.openapi(userBidsRoute, productController.getUserBids);

export { productRoutes };
