import { Context } from 'hono';
import productPricingService from '../services/productPricing.service';
import { errorResponse } from '../utils/response.util';

class ProductPricingController {
  /**
   * Initialize exchange rates (setup endpoint)
   */
  async initializeRates(c: Context) {
    try {
      console.log('🚀 Initializing exchange rates for product pricing...');
      
      const result = await productPricingService.initializeExchangeRates();
      
      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result);
    } catch (error) {
      console.error('Initialize rates controller error:', error);
      return c.json(errorResponse("An error occurred while initializing exchange rates"), 500);
    }
  }

  /**
   * Get products with converted prices
   */
  async getProductsWithPrices(c: Context) {
    try {
      const targetCurrency = c.req.query('currency') || 'IDR';
      const page = parseInt(c.req.query('page') || '1');
      const limit = parseInt(c.req.query('limit') || '20');

      // Validate pagination
      if (page < 1 || limit < 1 || limit > 100) {
        return c.json(errorResponse("Invalid pagination parameters"), 400);
      }

      // Validate currency
      const supportedCurrencies = ['USD', 'IDR'];
      if (!supportedCurrencies.includes(targetCurrency.toUpperCase())) {
        return c.json(errorResponse("Unsupported currency. Supported: USD, IDR"), 400);
      }

      const result = await productPricingService.getProductsWithConvertedPrices(
        targetCurrency.toUpperCase(),
        page,
        limit
      );

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result);
    } catch (error) {
      console.error('Get products with prices controller error:', error);
      return c.json(errorResponse("An error occurred while getting products with prices"), 500);
    }
  }

  /**
   * Convert single price
   */
  async convertPrice(c: Context) {
    try {
      const body = await c.req.json();
      const { price, fromCurrency, toCurrency } = body;

      // Validate required fields
      if (!price || !fromCurrency || !toCurrency) {
        return c.json(errorResponse("Missing required fields: price, fromCurrency, toCurrency"), 400);
      }

      // Validate price
      const numPrice = Number(price);
      if (isNaN(numPrice) || numPrice <= 0) {
        return c.json(errorResponse("Price must be a positive number"), 400);
      }

      // Validate currencies
      const supportedCurrencies = ['USD', 'IDR'];
      if (!supportedCurrencies.includes(fromCurrency.toUpperCase()) || 
          !supportedCurrencies.includes(toCurrency.toUpperCase())) {
        return c.json(errorResponse("Unsupported currency. Supported: USD, IDR"), 400);
      }

      const conversion = await productPricingService.convertProductPrice(
        numPrice,
        fromCurrency.toUpperCase(),
        toCurrency.toUpperCase()
      );

      return c.json({
        status: true,
        message: "Price conversion completed successfully",
        data: conversion
      });

    } catch (error) {
      console.error('Convert price controller error:', error);
      return c.json(errorResponse("An error occurred while converting price"), 500);
    }
  }

  /**
   * Get current exchange rate for pricing
   */
  async getCurrentRate(c: Context) {
    try {
      const fromCurrency = c.req.query('from')?.toUpperCase() || 'USD';
      const toCurrency = c.req.query('to')?.toUpperCase() || 'IDR';

      // Validate currencies
      const supportedCurrencies = ['USD', 'IDR'];
      if (!supportedCurrencies.includes(fromCurrency) || !supportedCurrencies.includes(toCurrency)) {
        return c.json(errorResponse("Unsupported currency. Supported: USD, IDR"), 400);
      }

      const rateInfo = await productPricingService.getExchangeRateForPricing(fromCurrency, toCurrency);

      return c.json({
        status: true,
        message: "Exchange rate retrieved successfully",
        data: {
          fromCurrency,
          toCurrency,
          rate: rateInfo.rate,
          source: rateInfo.source,
          date: rateInfo.date,
          lastUpdated: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Get current rate controller error:', error);
      return c.json(errorResponse("An error occurred while getting exchange rate"), 500);
    }
  }

  /**
   * Force refresh exchange rate
   */
  async refreshRate(c: Context) {
    try {
      const fromCurrency = c.req.query('from')?.toUpperCase() || 'USD';
      const toCurrency = c.req.query('to')?.toUpperCase() || 'IDR';

      // Only allow USD-IDR for now
      if (!((fromCurrency === 'USD' && toCurrency === 'IDR') || (fromCurrency === 'IDR' && toCurrency === 'USD'))) {
        return c.json(errorResponse("Only USD-IDR conversion supported for refresh"), 400);
      }

      console.log(`🔄 Force refreshing exchange rate: ${fromCurrency}/${toCurrency}`);

      // This will fetch fresh rate from API and save to database
      const rateInfo = await productPricingService.getExchangeRateForPricing(fromCurrency, toCurrency);

      return c.json({
        status: true,
        message: "Exchange rate refreshed successfully",
        data: {
          fromCurrency,
          toCurrency,
          rate: rateInfo.rate,
          source: rateInfo.source,
          refreshedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Refresh rate controller error:', error);
      return c.json(errorResponse("An error occurred while refreshing exchange rate"), 500);
    }
  }

  /**
   * Get exchange rate status and info
   */
  async getRateStatus(c: Context) {
    try {
      // Check if we have today's rates
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // This would need to be implemented in the service
      const status = {
        hasRatesForToday: true, // Placeholder
        lastUpdate: new Date().toISOString(),
        supportedCurrencies: ['USD', 'IDR'],
        defaultMargin: 0.02,
        rateSource: 'api',
        nextUpdate: 'Daily at 6:00 AM Jakarta Time'
      };

      return c.json({
        status: true,
        message: "Exchange rate status retrieved successfully",
        data: status
      });

    } catch (error) {
      console.error('Get rate status controller error:', error);
      return c.json(errorResponse("An error occurred while getting rate status"), 500);
    }
  }

  /**
   * Test price conversion with sample data
   */
  async testConversion(c: Context) {
    try {
      const testCases = [
        { price: 100, from: 'USD', to: 'IDR' },
        { price: 1500000, from: 'IDR', to: 'USD' },
        { price: 50, from: 'USD', to: 'IDR' },
        { price: 750000, from: 'IDR', to: 'USD' }
      ];

      const results = [];

      for (const testCase of testCases) {
        try {
          const conversion = await productPricingService.convertProductPrice(
            testCase.price,
            testCase.from,
            testCase.to
          );
          
          results.push({
            input: testCase,
            output: conversion,
            success: true
          });
        } catch (error) {
          results.push({
            input: testCase,
            error: error.message,
            success: false
          });
        }
      }

      return c.json({
        status: true,
        message: "Price conversion test completed",
        data: {
          testCases: results,
          summary: {
            total: testCases.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length
          }
        }
      });

    } catch (error) {
      console.error('Test conversion controller error:', error);
      return c.json(errorResponse("An error occurred during conversion test"), 500);
    }
  }
}

const productPricingController = new ProductPricingController();
export default productPricingController;
