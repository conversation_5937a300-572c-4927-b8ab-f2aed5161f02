import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import productService from "../services/product.service";

class ProductController {
  async createProduct(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await productService.createProduct(body, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getProducts(c: Context) {
    try {
      const rawQuery = c.req.query();

      // Parse and validate query parameters
      const query = {
        page: parseInt(rawQuery.page || '1'),
        limit: parseInt(rawQuery.limit || '10'),
        sortOrder: (rawQuery.sortOrder as 'asc' | 'desc') || 'desc',
        sortBy: (rawQuery.sortBy as 'createdAt' | 'priceUSD' | 'auctionEndDate' | 'bidCount') || 'createdAt',
        status: rawQuery.status as 'active' | 'draft' | 'sold' | 'cancelled' | undefined,
        search: rawQuery.search,
        categoryId: rawQuery.categoryId,
        sellType: rawQuery.sellType as 'auction' | 'buy-now' | undefined,
        itemTypeId: rawQuery.itemTypeId,
        sellerId: rawQuery.sellerId, // Add sellerId parameter
        minPrice: rawQuery.minPrice ? parseFloat(rawQuery.minPrice) : undefined,
        maxPrice: rawQuery.maxPrice ? parseFloat(rawQuery.maxPrice) : undefined,
      };

      const result = await productService.getProducts(query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get products controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  /**
   * Get products with converted prices for display
   */
  async getProductsWithPricing(c: Context) {
    try {
      const targetCurrency = c.req.query('currency') || 'IDR';
      const page = parseInt(c.req.query('page') || '1');
      const limit = parseInt(c.req.query('limit') || '20');

      // Validate pagination
      if (page < 1 || limit < 1 || limit > 100) {
        return c.json(errorResponse("Invalid pagination parameters"), 400);
      }

      // Validate currency
      const supportedCurrencies = ['USD', 'IDR'];
      if (!supportedCurrencies.includes(targetCurrency.toUpperCase())) {
        return c.json(errorResponse("Unsupported currency. Supported: USD, IDR"), 400);
      }

      const result = await productService.getProductsWithConvertedPrices(
        targetCurrency.toUpperCase(),
        page,
        limit
      );

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get products with pricing controller error:", error);
      return c.json(
        errorResponse("An error occurred while retrieving products with pricing"),
        500
      );
    }
  }

  async getProductById(c: Context) {
    try {
      const id = c.req.param('id');
      const result = await productService.getProductById(id);

      if (!result.status) {
        return c.json(result, 404);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getProductBySlug(c: Context) {
    try {
      const { slug } = c.req.param();
      const result = await productService.getProductBySlug(slug);

      if (!result.status) {
        return c.json(result, 404);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get product by slug controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateProduct(c: Context) {
    try {
      const id = c.req.param('id');
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await productService.updateProduct(id, body, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async deleteProduct(c: Context) {
    try {
      const id = c.req.param('id');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await productService.deleteProduct(id, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Delete product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getCategories(c: Context) {
    try {
      const rawQuery = c.req.query();
      const query = {
        sellType: rawQuery.sellType as 'auction' | 'buy-now' | undefined,
      };
      const result = await productService.getCategories(query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get categories controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createCategory(c: Context) {
    try {
      const body = await c.req.json();
      const result = await productService.createCategory(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create category controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getItemTypes(c: Context) {
    try {
      const categoryId = c.req.query('categoryId');
      const result = await productService.getItemTypes(categoryId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get item types controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createItemType(c: Context) {
    try {
      const body = await c.req.json();
      const result = await productService.createItemType(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create item type controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async placeBid(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      console.log("Place bid body:", body);

      const result = await productService.placeBid(body, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Place bid controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async uploadImages(c: Context) {
    try {
      const { parseMultipartData, uploadImagesToCloudinary, saveFilesLocally } = await import('../middlewares/upload');

      // Parse multipart form data
      const files = await parseMultipartData(c);

      if (!files || files.length === 0) {
        return c.json(
          errorResponse("No files uploaded"),
          400
        );
      }

      // Check if we're in development or production
      const isDevelopment = process.env.NODE_ENV === 'development';
      let urls: string[];

      if (isDevelopment || !process.env.CLOUDINARY_CLOUD_NAME) {
        // Use local storage for development
        urls = await saveFilesLocally(files);
      } else {
        // Use Cloudinary for production
        urls = await uploadImagesToCloudinary(files);
      }

      return c.json({
        status: true,
        message: "Images uploaded successfully",
        data: {
          urls,
          count: urls.length
        }
      }, 200);
    } catch (error) {
      console.error("Upload images controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "Failed to upload images"}`),
        500
      );
    }
  }

  async getBidsByProductId(c: Context) {
    try {
      console.log("Get bids by product ID controller called");
      const productId = c.req.param('productId');
      const result = await productService.getBidsByProductId(productId);

      if (!result.status) {
        return c.json(result, 404);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get bids by product ID controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getUserBids(c: Context) {
    try {
      const user = c.get('user');
      const id = c.req.param('id');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await productService.getUserBids(id, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get user bids controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new ProductController();
