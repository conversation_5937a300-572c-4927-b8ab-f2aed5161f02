import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import paymentService from "../services/payment.service";
import { prisma } from "../db";
import { CreateInvoiceInput, CreateEWalletChargeInput } from "../schemas/payment.schema";

class PaymentController {
  /**
   * Get payment details by payment ID
   */
  async getPaymentDetails(c: Context) {
    try {
      const paymentId = c.req.param('paymentId');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      if (!paymentId) {
        return c.json(errorResponse("Payment ID is required"), 400);
      }

      const result = await paymentService.getPaymentDetails(paymentId);

      if (!result.status) {
        return c.json(result, 400);
      }

      // Verify that the payment belongs to the user
      if (result?.data?.payment.order.userId !== user.id) {
        return c.json(errorResponse("Access denied"), 403);
      }

      return c.json({
        status: true,
        message: "Payment details retrieved successfully",
        data: result.data
      });
    } catch (error) {
      console.error('Get payment details error:', error);
      return c.json(errorResponse("Internal server error"), 500);
    }
  }

  /**
   * Create payment based on method type
   */
  async createPayment(c: Context) {
    try {
      const body = await c.req.json();
      const { orderId, paymentType, ...paymentData } = body;

      // Verify order exists and belongs to user
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const order = await prisma.order.findFirst({
        where: {
          id: orderId,
          userId: user.id,
        },
      });

      if (!order) {
        return c.json(errorResponse("Order not found or access denied"), 404);
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId },
      });

      if (existingPayment && existingPayment.status === 'PAID') {
        return c.json(errorResponse("Order already paid"), 400);
      }

      let result;

      // Route to appropriate payment method based on type
      switch (paymentType) {
        case 'ewallet':
          result = await paymentService.createEWalletCharge({
            orderId,
            ...paymentData
          });
          break;

        case 'virtual_account':
          result = await paymentService.createVirtualAccount({
            orderId,
            ...paymentData
          });
          break;

        case 'retail_outlet':
          result = await paymentService.createRetailOutlet({
            orderId,
            ...paymentData
          });
          break;

        case 'qr_code':
          result = await paymentService.createQRCode({
            orderId,
            ...paymentData
          });
          break;

        default:
          // Default to invoice for all other cases
          result = await paymentService.createInvoice({
            orderId,
            ...paymentData
          });
          break;
      }

      if (!result.status || !result.data) {
        return c.json(errorResponse(result.message || "Failed to create payment"), 400);
      }

      return c.json({
        status: true,
        message: "Payment created successfully",
        data: result.data
      }, 201);
    } catch (error: any) {
      console.error("Create payment error:", error);
      return c.json(errorResponse(error.message || "Failed to create payment"), 500);
    }
  }

  /**
   * Create Xendit invoice for payment
   */
  async createInvoice(c: Context) {
    try {
      const body = await c.req.json() as CreateInvoiceInput;

      // Verify order exists and belongs to user
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const order = await prisma.order.findFirst({
        where: {
          id: body.orderId,
          userId: user.id,
        },
      });

      if (!order) {
        return c.json(errorResponse("Order not found or access denied"), 404);
      }

      // Check if payment already exists
      const existingPayment = await prisma.payment.findFirst({
        where: { orderId: body.orderId },
      });

      if (existingPayment && existingPayment.status === 'PAID') {
        return c.json(errorResponse("Order already paid"), 400);
      }

      // Create invoice
      const result = await paymentService.createInvoice(body);

      if (!result.status || !result.data) {
        return c.json(errorResponse(result.message || "Failed to create invoice"), 400);
      }

      return c.json({
        status: true,
        message: "Invoice created successfully",
        data: result.data
      }, 201);
    } catch (error: any) {
      console.error("Create invoice error:", error);
      return c.json(errorResponse(error.message || "Failed to create invoice"), 500);
    }
  }

  /**
   * Create eWallet charge
   */
  async createEWalletCharge(c: Context) {
    try {
      const body = await c.req.json() as CreateEWalletChargeInput;
      
      // Verify order exists and belongs to user
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const order = await prisma.order.findFirst({
        where: {
          id: body.orderId,
          userId: user.id,
        },
      });

      if (!order) {
        return c.json(errorResponse("Order not found or access denied"), 404);
      }

      // Create eWallet charge
      const result = await paymentService.createEWalletCharge(body);

      if (!result.status || !result.data) {
        return c.json(errorResponse(result.message || "Failed to create eWallet charge"), 400);
      }

      return c.json({
        status: true,
        message: "eWallet charge created successfully",
        data: result.data
      }, 201);
    } catch (error: any) {
      console.error("Create eWallet charge error:", error);
      return c.json(errorResponse(error.message || "Failed to create eWallet charge"), 500);
    }
  }

  /**
   * Get invoice status from Xendit
   */
  async getInvoiceStatus(c: Context) {
    try {
      const invoiceId = c.req.param('invoiceId');
      
      if (!invoiceId) {
        return c.json(errorResponse("Invoice ID is required"), 400);
      }

      // Find payment by Xendit invoice ID
      const payment = await prisma.payment.findUnique({
        where: { xenditInvoiceId: invoiceId },
        include: { order: true },
      });

      if (!payment) {
        return c.json(errorResponse("Payment not found"), 404);
      }

      // Verify user access
      const user = c.get('user');
      if (!user || payment.order.userId !== user.id) {
        return c.json(errorResponse("Access denied"), 403);
      }

      return c.json({
        status: true,
        message: "Invoice status retrieved",
        data: {
          id: payment.id,
          status: payment.status,
          amount: Number(payment.amount),
          currency: payment.currency,
          paidAt: payment.paidAt?.toISOString() || null,
          paymentMethod: payment.paymentMethod,
          paymentChannel: payment.paymentChannel,
          invoiceUrl: payment.invoiceUrl,
        }
      });
    } catch (error: any) {
      console.error("Get invoice status error:", error);
      return c.json(errorResponse(error.message || "Failed to get invoice status"), 500);
    }
  }

  /**
   * Get payment status for an order
   */
  async getPaymentStatus(c: Context) {
    try {
      const orderId = c.req.param('orderId');
      
      if (!orderId) {
        return c.json(errorResponse("Order ID is required"), 400);
      }

      // Find payment by order ID
      const payment = await prisma.payment.findFirst({
        where: { orderId },
        include: { order: true },
        orderBy: { createdAt: 'desc' },
      });

      if (!payment) {
        return c.json(errorResponse("Payment not found"), 404);
      }

      // Verify user access
      const user = c.get('user');
      if (!user || payment.order.userId !== user.id) {
        return c.json(errorResponse("Access denied"), 403);
      }

      return c.json({
        status: true,
        message: "Payment status retrieved",
        data: {
          id: payment.id,
          orderId: payment.orderId,
          status: payment.status,
          amount: Number(payment.amount),
          currency: payment.currency,
          paidAt: payment.paidAt?.toISOString() || null,
          paymentMethod: payment.paymentMethod,
          paymentChannel: payment.paymentChannel,
          createdAt: payment.createdAt.toISOString(),
          updatedAt: payment.updatedAt.toISOString(),
        }
      });
    } catch (error: any) {
      console.error("Get payment status error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment status"), 500);
    }
  }

  /**
   * Create Virtual Account payment
   */
  async createVirtualAccount(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const body = await c.req.json();
      const result = await paymentService.createVirtualAccount(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Virtual Account created successfully",
        data: result.data
      });
    } catch (error) {
      console.error('Create Virtual Account error:', error);
      return c.json(errorResponse("Internal server error"), 500);
    }
  }

  /**
   * Create Retail Outlet payment
   */
  async createRetailOutlet(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const body = await c.req.json();
      const result = await paymentService.createRetailOutlet(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Retail Outlet payment created successfully",
        data: result.data
      });
    } catch (error) {
      console.error('Create Retail Outlet error:', error);
      return c.json(errorResponse("Internal server error"), 500);
    }
  }

  /**
   * Create QR Code payment
   */
  async createQRCode(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const body = await c.req.json();
      const result = await paymentService.createQRCode(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "QR Code payment created successfully",
        data: result.data
      });
    } catch (error) {
      console.error('Create QR Code error:', error);
      return c.json(errorResponse("Internal server error"), 500);
    }
  }

  /**
   * Handle Xendit webhook
   */
  async handleWebhook(c: Context) {
    try {
      const signature = c.req.header('x-callback-token') || '';
      const payload = await c.req.json();

      const result = await paymentService.handleWebhook(payload, signature);

      return c.json({
        status: true,
        message: "Webhook processed successfully",
        data: result
      });
    } catch (error: any) {
      console.error("Webhook handling error:", error);
      return c.json(errorResponse(error.message || "Failed to process webhook"), 500);
    }
  }

  /**
   * Get supported payment methods
   */
  async getPaymentMethods(c: Context) {
    console.log('Get payment methods request');
    try {
      const currency = c.req.query('currency') || 'USD';
      console.log('Payment methods request - currency:', currency);

      if (!['IDR', 'USD'].includes(currency)) {
        return c.json(errorResponse("Valid currency (IDR or USD) is required"), 400);
      }

      // Get payment methods from database
      const paymentMethods = await prisma.paymentMethod.findMany({
        where: {
          currency: currency,
          isActive: true
        },
        orderBy: [
          { isRecommended: 'desc' },
          { name: 'asc' }
        ]
      });

      console.log("Payment methods retrieved:", paymentMethods.length);

      // Group by type for easier frontend consumption
      const groupedMethods = paymentMethods.reduce((acc, method) => {
        if (!acc[method.type]) {
          acc[method.type] = [];
        }
        acc[method.type].push({
          id: method.id,
          name: method.name,
          description: method.description,
          type: method.type,
          currency: method.currency,
          isRecommended: method.isRecommended,
          icon: method.icon,
          processingFee: Number(method.processingFee),
          processingFeeType: method.processingFeeType,
          minAmount: Number(method.minAmount),
          maxAmount: Number(method.maxAmount),
          supportedCountries: method.supportedCountries,
          features: method.features,
          xenditConfig: method.xenditConfig
        });
        return acc;
      }, {} as any);

      return c.json({
        status: true,
        message: "Payment methods retrieved",
        data: groupedMethods
      });
    } catch (error: any) {
      console.error("Get payment methods error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment methods"), 500);
    }
  }

  /**
   * Get user's payment history
   */
  async getPaymentHistory(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const { page = "1", limit = "10", status } = c.req.query();

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;

      const where: any = {
        order: {
          userId: user.id,
        },
      };

      if (status) {
        where.status = status;
      }

      const [payments, total] = await Promise.all([
        prisma.payment.findMany({
          where,
          include: {
            order: {
              select: {
                orderNumber: true,
                total: true,
                createdAt: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limitNum,
        }),
        prisma.payment.count({ where }),
      ]);

      const formattedPayments = payments.map(payment => ({
        id: payment.id,
        orderId: payment.orderId,
        orderNumber: payment.order.orderNumber,
        status: payment.status,
        amount: Number(payment.amount),
        currency: payment.currency,
        paymentMethod: payment.paymentMethod,
        paidAt: payment.paidAt?.toISOString() || null,
        createdAt: payment.createdAt.toISOString(),
      }));

      return c.json({
        status: true,
        message: "Payment history retrieved",
        data: {
          payments: formattedPayments,
          pagination: {
            total,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(total / limitNum),
          },
        }
      });
    } catch (error: any) {
      console.error("Get payment history error:", error);
      return c.json(errorResponse(error.message || "Failed to get payment history"), 500);
    }
  }
}

const paymentController = new PaymentController();
export default paymentController;
