# 🏷️ Product Pricing with Exchange Rate Guide

## 🎯 Sistem Exchange Rate untuk Product Pricing

Sistem ini otomatis fetch exchange rate dari API, simpan ke database, dan gunakan untuk konversi harga produk secara real-time.

---

## 🚀 Quick Setup (5 Menit)

### **Step 1: Initialize Exchange Rates**
```bash
# Setup exchange rate untuk pertama kali
curl -X POST http://localhost:3000/product-pricing/initialize
```

**Response:**
```json
{
  "status": true,
  "message": "Exchange rates initialized successfully",
  "data": {
    "usdToIdr": 15539.25,
    "source": "api",
    "date": "2024-01-15T00:00:00.000Z"
  }
}
```

### **Step 2: Test Product Pricing**
```bash
# Get products dengan harga terkonversi ke IDR
curl "http://localhost:3000/products/with-pricing?currency=IDR&page=1&limit=10"
```

### **Step 3: Test Price Conversion**
```bash
# Convert harga manual
curl -X POST http://localhost:3000/product-pricing/convert \
  -H "Content-Type: application/json" \
  -d '{
    "price": 100,
    "fromCurrency": "USD",
    "toCurrency": "IDR"
  }'
```

---

## 📊 API Endpoints

### **🔧 Setup & Management**

#### **Initialize Exchange Rates**
```bash
POST /product-pricing/initialize
```
- Fetch rate dari API eksternal
- Simpan ke database dengan margin 2%
- Setup USD-IDR dan IDR-USD rates

#### **Get Rate Status**
```bash
GET /product-pricing/status
```
- Check apakah ada rate hari ini
- Info last update dan source

#### **Refresh Rate**
```bash
POST /product-pricing/rate/refresh?from=USD&to=IDR
```
- Force refresh rate dari API
- Update database dengan rate terbaru

### **💱 Exchange Rate Operations**

#### **Get Current Rate**
```bash
GET /product-pricing/rate?from=USD&to=IDR
```

**Response:**
```json
{
  "status": true,
  "data": {
    "fromCurrency": "USD",
    "toCurrency": "IDR", 
    "rate": 15539.25,
    "source": "database",
    "date": "2024-01-15T00:00:00.000Z"
  }
}
```

#### **Convert Price**
```bash
POST /product-pricing/convert
Content-Type: application/json

{
  "price": 100,
  "fromCurrency": "USD",
  "toCurrency": "IDR"
}
```

**Response:**
```json
{
  "status": true,
  "data": {
    "originalPrice": 100,
    "originalCurrency": "USD",
    "convertedPrice": 1553925.00,
    "targetCurrency": "IDR",
    "exchangeRate": 15539.25
  }
}
```

### **🛍️ Product Pricing**

#### **Get Products with Converted Prices**
```bash
GET /products/with-pricing?currency=IDR&page=1&limit=20
```

**Response:**
```json
{
  "status": true,
  "data": {
    "products": [
      {
        "id": "prod123",
        "itemName": "Vintage Watch",
        "originalPrice": 100,
        "originalCurrency": "USD",
        "price": 1553925.00,
        "convertedPrice": 1553925.00,
        "displayCurrency": "IDR",
        "exchangeRate": 15539.25,
        "priceSource": "converted",
        "auctionStatus": "active",
        "currentBid": 1650000.00,
        "bidCount": 5
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150
    },
    "targetCurrency": "IDR"
  }
}
```

#### **Get Products (Original - No Conversion)**
```bash
GET /products?page=1&limit=20
```
- Returns products dengan harga original
- No currency conversion

---

## 🔄 How It Works

### **1. Exchange Rate Flow**
```
API Call → Fetch Live Rate → Add 2% Margin → Save to Database → Use for Conversion
```

### **2. Product Pricing Flow**
```
Product Request → Check Currency → Get Exchange Rate → Convert Price → Return with Both Prices
```

### **3. Database Storage**
```sql
-- Currency rates disimpan dengan struktur:
CurrencyRate {
  fromCurrency: "USD"
  toCurrency: "IDR"
  rate: 15234.56        -- Live rate
  sellRate: 15539.25    -- rate + 2% margin
  buyRate: 14929.87     -- rate - 2% margin
  date: "2024-01-15"
  source: "api"
}
```

### **4. Margin Protection**
- **Sell Rate**: `live_rate × 1.02` (untung 2% saat jual ke customer)
- **Buy Rate**: `live_rate × 0.98` (untung 2% saat beli dari customer)

---

## 🧪 Testing Scenarios

### **Test 1: Basic Conversion**
```bash
# Test convert $100 to IDR
curl -X POST http://localhost:3000/product-pricing/convert \
  -H "Content-Type: application/json" \
  -d '{"price": 100, "fromCurrency": "USD", "toCurrency": "IDR"}'

# Expected: ~1,550,000 IDR (depending on current rate)
```

### **Test 2: Product Listing**
```bash
# Get products in IDR
curl "http://localhost:3000/products/with-pricing?currency=IDR"

# Get products in USD  
curl "http://localhost:3000/products/with-pricing?currency=USD"
```

### **Test 3: Rate Management**
```bash
# Check current rate
curl "http://localhost:3000/product-pricing/rate?from=USD&to=IDR"

# Refresh rate
curl -X POST "http://localhost:3000/product-pricing/rate/refresh?from=USD&to=IDR"

# Check rate again (should be updated)
curl "http://localhost:3000/product-pricing/rate?from=USD&to=IDR"
```

### **Test 4: Comprehensive Test**
```bash
# Run all conversion tests
curl "http://localhost:3000/product-pricing/test-conversion"
```

---

## 🛡️ Error Handling & Fallbacks

### **API Failure Handling**
1. **Primary API fails** → Try alternative API
2. **All APIs fail** → Use fallback rate (15,000 IDR/USD)
3. **Database error** → Use static calculation
4. **Invalid input** → Return error with validation message

### **Rate Freshness**
- **Today's rate exists** → Use from database
- **No rate for today** → Fetch from API and save
- **API unavailable** → Use latest rate from database
- **No rates at all** → Use fallback rate

### **Product Display**
- **Conversion successful** → Show converted price
- **Conversion fails** → Show original price
- **Mixed currencies** → Show both original and converted
- **Unsupported currency** → Show original with warning

---

## 📈 Production Considerations

### **Performance**
- Exchange rates cached in database
- Only fetch from API once per day
- Product conversion uses cached rates
- Batch processing for multiple products

### **Accuracy**
- 2% margin protects against rate fluctuations
- Daily rate updates ensure freshness
- Multiple API sources for reliability
- Fallback rates prevent system failure

### **Monitoring**
```bash
# Check rate status
curl "http://localhost:3000/product-pricing/status"

# Monitor conversion success
curl "http://localhost:3000/product-pricing/test-conversion"
```

### **Maintenance**
- Rates auto-update daily via scheduler
- Manual refresh available for urgent updates
- Historical rates stored for audit
- Margin adjustable per business needs

---

## 🎯 Integration Examples

### **Frontend Integration**
```javascript
// Get products with IDR pricing
const response = await fetch('/products/with-pricing?currency=IDR');
const data = await response.json();

data.products.forEach(product => {
  console.log(`${product.itemName}: ${product.convertedPrice} ${product.displayCurrency}`);
  console.log(`Original: ${product.originalPrice} ${product.originalCurrency}`);
  console.log(`Exchange Rate: ${product.exchangeRate}`);
});
```

### **Price Display Logic**
```javascript
function displayPrice(product) {
  if (product.priceSource === 'converted') {
    return `${product.convertedPrice.toLocaleString()} ${product.displayCurrency}`;
  } else {
    return `${product.originalPrice.toLocaleString()} ${product.originalCurrency}`;
  }
}
```

---

## 🚨 Troubleshooting

### **Problem: No rates available**
```bash
# Solution: Initialize rates
curl -X POST http://localhost:3000/product-pricing/initialize
```

### **Problem: Conversion returns error**
```bash
# Check rate status
curl "http://localhost:3000/product-pricing/status"

# Refresh rates
curl -X POST "http://localhost:3000/product-pricing/rate/refresh"
```

### **Problem: Products show wrong prices**
```bash
# Test conversion
curl "http://localhost:3000/product-pricing/test-conversion"

# Check specific rate
curl "http://localhost:3000/product-pricing/rate?from=USD&to=IDR"
```

---

## ✅ Success Checklist

- [ ] Exchange rates initialized
- [ ] Products show converted prices
- [ ] Conversion API works
- [ ] Rate refresh works
- [ ] Fallback rates available
- [ ] Error handling tested
- [ ] Performance acceptable
- [ ] Margin protection active

**Sistem siap production! 🚀**
