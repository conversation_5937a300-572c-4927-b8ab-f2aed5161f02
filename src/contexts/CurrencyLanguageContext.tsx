'use client'
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useLocale } from 'next-intl'
import { getCurrencySymbol as getCurrencySymbolUtil } from '@/utils/currency'
import { useCurrencyState } from '@/hooks/useCurrencyState'
import { useCurrencyService } from '@/services/useCurrencyService'

// Types
export type SupportedCurrency = 'USD' | 'IDR'
export type SupportedLanguage = 'en' | 'id'

interface CurrencyLanguageContextType {
  // Currency state
  currency: SupportedCurrency
  setCurrency: (currency: SupportedCurrency) => void

  // Language state
  language: SupportedLanguage
  setLanguage: (language: SupportedLanguage) => void

  // Exchange rate state (from backend)
  exchangeRate: number
  isLoadingRate: boolean
  lastUpdated: string | null
  refreshExchangeRate: () => Promise<void>

  // Helper functions
  formatPrice: (amount: number, fromCurrency?: SupportedCurrency) => string
  convertPrice: (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency) => number
  convertAndFormatPrice: (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency) => string
  getCurrencySymbol: () => string
  getLanguageLabel: () => string
  getCurrencyLabel: () => string

  // Status
  isReady: boolean
}

const CurrencyLanguageContext = createContext<CurrencyLanguageContextType | undefined>(undefined)

interface CurrencyLanguageProviderProps {
  children: ReactNode
}

export const CurrencyLanguageProvider: React.FC<CurrencyLanguageProviderProps> = ({ children }) => {
  const router = useRouter()
  const pathname = usePathname()
  const currentLocale = useLocale() as SupportedLanguage

  // Initialize states
  const [language, setLanguageState] = useState<SupportedLanguage>(currentLocale)
  const [initialCurrency, setInitialCurrency] = useState<SupportedCurrency>('USD')

  // Load saved preferences from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCurrency = localStorage.getItem('preferred-currency') as SupportedCurrency
      const savedLanguage = localStorage.getItem('preferred-language') as SupportedLanguage

      if (savedCurrency && ['USD', 'IDR'].includes(savedCurrency)) {
        setInitialCurrency(savedCurrency)
      } else {
        // Set default currency based on language
        setInitialCurrency(currentLocale === 'id' ? 'IDR' : 'USD')
      }

      if (savedLanguage && ['en', 'id'].includes(savedLanguage)) {
        setLanguageState(savedLanguage)
      } else {
        setLanguageState(currentLocale)
      }
    }
  }, [currentLocale])

  // Update language when locale changes
  useEffect(() => {
    setLanguageState(currentLocale)
  }, [currentLocale])

  // Use new currency service for better performance
  const currencyService = useCurrencyService()

  // Currency state
  const [currency, setCurrencyState] = useState<SupportedCurrency>(initialCurrency)

  // Currency setter with localStorage persistence and query invalidation
  const setCurrency = async (newCurrency: SupportedCurrency) => {
    console.log(`🔄 Switching currency from ${currency} to ${newCurrency}`)

    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-currency', newCurrency)
    }

    // Update currency state
    setCurrencyState(newCurrency)

    // Invalidate all currency-related queries to trigger re-fetch
    await currencyService.invalidateCurrencyQueries()

    console.log(`✅ Currency switched to ${newCurrency}`)
  }

  // Language setter with navigation and localStorage persistence
  const setLanguage = (newLanguage: SupportedLanguage) => {
    setLanguageState(newLanguage)
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-language', newLanguage)
    }
    
    // Navigate to new locale
    const currentPath = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    router.push(`/${newLanguage}${currentPath}`)
  }

  // Helper functions using currency service
  const formatPrice = (amount: number, fromCurrency?: SupportedCurrency): string => {
    const targetCurrency = fromCurrency || currency
    console.log(`Formatting ${amount} as ${targetCurrency}`)
    return currencyService.formatPrice(amount, targetCurrency)
  }

  const convertPrice = (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency): number => {
    const targetCurrency = toCurrency || currency
    console.log(`Converting ${amount} from ${fromCurrency} to ${targetCurrency}`)
    if (fromCurrency === targetCurrency) return amount
    return currencyService.convertPrice(amount, fromCurrency, targetCurrency)
  }

  const convertAndFormatPrice = (amount: number, fromCurrency: SupportedCurrency, toCurrency?: SupportedCurrency): string => {
    const targetCurrency = toCurrency || currency
    const converted = currencyService.convertPrice(amount, fromCurrency, targetCurrency)
    return currencyService.formatPrice(converted, targetCurrency)
  }



  const getCurrencySymbol = (): string => {
    return getCurrencySymbolUtil(currency)
  }

  const getLanguageLabel = (): string => {
    return language === 'en' ? 'English' : 'Bahasa Indonesia'
  }

  const getCurrencyLabel = (): string => {
    return currency === 'USD' ? 'USD ($)' : 'IDR (Rp)'
  }

  const value: CurrencyLanguageContextType = {
    currency,
    setCurrency,
    language,
    setLanguage,

    // Exchange rate state from currency service
    exchangeRate: currencyService.exchangeRate,
    isLoadingRate: currencyService.isLoading,
    lastUpdated: currencyService.lastUpdated || null,
    refreshExchangeRate: currencyService.refreshExchangeRate,

    // Helper functions
    formatPrice,
    convertPrice,
    convertAndFormatPrice,
    getCurrencySymbol,
    getLanguageLabel,
    getCurrencyLabel,

    // Status
    isReady: currencyService.isReady,
  }

  return (
    <CurrencyLanguageContext.Provider value={value}>
      {children}
    </CurrencyLanguageContext.Provider>
  )
}

// Custom hook to use the context
export const useCurrencyLanguage = (): CurrencyLanguageContextType => {
  const context = useContext(CurrencyLanguageContext)
  if (context === undefined) {
    throw new Error('useCurrencyLanguage must be used within a CurrencyLanguageProvider')
  }
  return context
}

// Currency options for dropdowns
export const CURRENCY_OPTIONS = [
  {
    value: 'USD' as const,
    label: 'USD ($)',
    symbol: '$',
    name: 'US Dollar',
    flag: '🇺🇸',
  },
  {
    value: 'IDR' as const,
    label: 'IDR (Rp)',
    symbol: 'Rp',
    name: 'Indonesian Rupiah',
    flag: '🇮🇩',
  },
]

// Language options for dropdowns
export const LANGUAGE_OPTIONS = [
  {
    value: 'en' as const,
    label: 'English',
    name: 'English',
    flag: '🇺🇸',
  },
  {
    value: 'id' as const,
    label: 'Bahasa Indonesia',
    name: 'Indonesian',
    flag: '🇮🇩',
  },
]

// Helper function to get currency by language
export const getCurrencyByLanguage = (language: SupportedLanguage): SupportedCurrency => {
  return language === 'id' ? 'IDR' : 'USD'
}

// Helper function to get language by currency
export const getLanguageByCurrency = (currency: SupportedCurrency): SupportedLanguage => {
  return currency === 'IDR' ? 'id' : 'en'
}
