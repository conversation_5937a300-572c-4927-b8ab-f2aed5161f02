'use client'
import { Box, Heading, Text, VStack, Skeleton, Stack, Button } from '@chakra-ui/react';
import { useParams, useRouter } from 'next/navigation';
import ProductImageGallery from '@/components/product/ProductImageGallery';
import ProductDetailLayout from '@/components/product/ProductDetailLayout';
import AuctionInfo from '@/components/product/AuctionInfo';
import BuyNowInfo from '@/components/product/BuyNowInfo';
import SalesHistory from '@/components/product/SalesHistory';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { useProductBySlugQuery } from '@/services/useProductQuery';
import { useAddToCartMutation, useCartQuery } from '@/services/useCartQuery';
import { useBuyNowMutation } from '@/services/useCheckoutQuery';
import { formatDistanceToNow } from 'date-fns';
import ProductCategory from '@/components/product/ProductCategory';
import products from '@/datas/product.json';
import ProductInfo from '@/components/product/ProductInfo';
import { useSession } from 'next-auth/react';

const ProductDetailPage = () => {
    const dataSession = useSession()
    const session = dataSession.data;
    const { locale, category, slug } = useParams();
    const { data: cart, isLoading: isLoadingCart } = useCartQuery();
    const router = useRouter();

    const {
        data: product,
        isLoading,
        error,
        isError
    } = useProductBySlugQuery(slug as string);

    const addToCartMutation = useAddToCartMutation();
    const buyNowMutation = useBuyNowMutation();

    if (isLoading) {
        return (
            <Box>
                <ProductDetailLayout
                    leftContent={
                        <VStack gap={4} align="stretch">
                            <Box px={{ base: 0, md: 6 }}>
                                <Skeleton height="20px" width="300px" />
                            </Box>
                            <Box px={{ base: 0, md: 6 }}>
                                <Skeleton height={{ base: '300px', lg: '500px', xl: '650px' }} />
                            </Box>
                        </VStack>
                    }
                    rightContent={
                        <Stack gap={6}>
                            <Skeleton height="40px" />
                            <Skeleton height="20px" />
                            <Skeleton height="100px" />
                            <Skeleton height="200px" />
                        </Stack>
                    }
                />
            </Box>
        );
    }

    if (isError || !product) {
        return (
            <Box textAlign="center" py={20}>
                <Text fontSize="xl" color="red.500" mb={4}>
                    {error instanceof Error ? error.message : 'Product not found'}
                </Text>
                <Text color="gray.600">
                    The product you're looking for doesn't exist or has been removed.
                </Text>
            </Box>
        );
    }

    const breadcrumbItems = [
        {
            label: 'Home',
            href: '/'
        },
        {
            label: 'Auction',
            href: '/auction'
        },
        {
            label: product.itemName,
            isCurrentPage: true
        }
    ];

    const handleAddToCart = async (productId: string, quantity: number) => {
        if (cart?.items.some(item => item.productId === productId) || isLoading || !product?.id || isLoadingCart) {
            console.warn('Item already exists in cart');
            return;
        }
        try {
            await addToCartMutation.mutateAsync({
                productId,
                quantity
            });
        } catch (error) {
            console.error('Failed to add to cart:', error);
        }
    };

    const handleBuyNow = async (productId: string, quantity: number) => {
        try {
            router.push(`/checkout?productId=${productId}`);
        } catch (error) {
            console.error('Failed to buy now:', error);
        }
    };

    const handlePlaceBid = () => {
        console.log('Place bid for product:', product.id);
    };

    const handleShowBidHistory = () => {
        console.log('Show bid history for product:', product.id);
    };

    const handleViewSalesHistory = () => {
        console.log('View sales history for product:', product.id);
        // TODO: Implement sales history view logic
    };

    const transformedProduct = {
        id: product.id,
        slug: product.slug || '',
        title: product.itemName,
        image: product.images.find(img => img.isMain)?.imageUrl || product.images[0]?.imageUrl || '',
        images: product.images.map(img => img.imageUrl), // ProductItem expects string[] for images
        price: `$${product.priceUSD}`, // ProductItem expects string for price
        bids: product.bidCount?.toString() || '0', // ProductItem expects string for bids
    };

    const leftContent = (
        <VStack gap={4} align="stretch">
            <Box px={{ base: 0, md: 6 }}>
                <Breadcrumb items={breadcrumbItems} />
            </Box>

            <Box
                position="sticky"
                top={{ base: 4, md: 8 }}
                px={{ base: 0, md: 6 }}
            >
                <ProductImageGallery
                    item={transformedProduct}
                    boxSizeWatchList={6}
                    containerProps={{
                        height: { base: 'full', lg: '500px', xl: '650px' },
                    }}
                />
            </Box>
        </VStack>
    );

    const rightContent = (
        <>
            <ProductInfo
                title={product.itemName}
                subtitle={product.category?.name || '-'}
                mb={6}
            />
            {
                dataSession.status == "loading" && (
                    <Skeleton
                        width="full"
                        height="300px"
                        borderRadius="lg" />
                )
            }

            {product.sellType === 'auction' ? (
                <AuctionInfo
                    currentBid={product.currentBid ? Number(product.currentBid) : Number(product.priceUSD)}
                    startingPrice={Number(product.priceUSD)}
                    bidCount={product.bidCount ?? 0}
                    auctionStartDate={product.auctionStartDate || new Date().toISOString()}
                    auctionEndDate={product.auctionEndDate || new Date().toISOString()}
                    extendedBiddingEnabled={product.extendedBiddingEnabled ?? false}
                    extendedBiddingMinutes={product.extendedBiddingMinutes ?? 5}
                    extendedBiddingDuration={product.extendedBiddingDuration ?? 10}
                    auctionStatus={product.auctionStatus || 'active'}
                    timeLeft={product.timeLeft || 'N/A'}
                    productId={product.id}
                    productName={product.itemName}
                    onPlaceBid={handlePlaceBid}
                    onShowBidHistory={handleShowBidHistory}
                    mb={6}
                    isLoadingAuth={dataSession.status == "loading"}
                    isAuthenticated={session?.user?.id ? true : false}
                />
            ) : (
                <BuyNowInfo
                    price={Number(product.priceUSD)}
                    productId={product.id}
                    productName={product.itemName}
                    onAddToCart={handleAddToCart}
                    isInCart={cart?.items.some(item => item.productId === product.id)}
                    onBuyNow={handleBuyNow}
                    mb={6}
                    isLoadingAuth={dataSession.status == "loading"}
                    isAuthenticated={session?.user?.id ? true : false}
                />
            )}

            <Box as="hr" my={6} borderColor="gray.200" />

            <Box>
                <Heading as="h3" size="md" mb={3}>
                    Description
                </Heading>
                <Text fontSize="sm" mb={4}>
                    {product.description || 'No description available for this product.'}
                </Text>
            </Box>

            <Box as="hr" my={6} borderColor="gray.200" />

            <SalesHistory
                onLinkClick={handleViewSalesHistory}
            />
        </>
    );

    return (
        <Box>
            <Box>
                <ProductDetailLayout
                    leftContent={leftContent}
                    rightContent={rightContent}
                />
            </Box>

            <ProductCategory
                items={products}
                title="Related Items"
            />

            <ProductCategory
                items={products}
                title="Similar Items"
            />
        </Box>
    );
};

export default ProductDetailPage;