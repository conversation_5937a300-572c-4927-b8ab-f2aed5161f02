
interface BuildUrlState {
    baseUrl: string;
    query: Record<string, any>;
}

export const buildUrl = ({ baseUrl, query }: BuildUrlState): string => {
    const queryString = new URLSearchParams(query).toString();

    return `${baseUrl}?${queryString}`;
};

export const formatUSD = (value: number) =>
    new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    }).format(value);

export const formatCurrency = (value: number, currency: 'USD' | 'IDR' = 'USD') =>
    new Intl.NumberFormat(currency === 'USD' ? 'en-US' : 'id-ID', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: currency === 'IDR' ? 0 : 2,
        maximumFractionDigits: currency === 'IDR' ? 0 : 2,
    }).format(value);

export const parseUSD = (value: string) =>
    parseFloat(value.replace(/[^0-9.]/g, '')) || 0;

export const getTimeLeftString = (endDateStr: string): string => {
  const now: Date = new Date();
  const endDate: Date = new Date(endDateStr);
  const diffMs: number = endDate.getTime() - now.getTime();

  if (diffMs <= 0) return "Ended";

  const totalMinutes: number = Math.floor(diffMs / 60000);
  const days: number = Math.floor(totalMinutes / (24 * 60));
  const hours: number = Math.floor((totalMinutes % (24 * 60)) / 60);
  const minutes: number = totalMinutes % 60;

  return `${days}D ${hours}H ${minutes}M`;
};
