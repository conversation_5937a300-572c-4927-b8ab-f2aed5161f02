'use client'
import React from 'react'
import {
  Box,
  Button,
  HStack,
  Text,
  VStack,
  Separator,
} from '@chakra-ui/react'
import { useCurrencyLanguage, CURRENCY_OPTIONS, LANGUAGE_OPTIONS } from '@/contexts/CurrencyLanguageContext'
import { useTranslations } from 'next-intl'

const CurrencyLanguageSelector: React.FC = () => {
  const t = useTranslations()
  const {
    currency,
    setCurrency,
    language,
    setLanguage,
    getCurrencyLabel,
    getLanguageLabel,
  } = useCurrencyLanguage()

  return (
    <HStack gap={2}>
      {/* Currency Selector - Enhanced with loading state */}
      <Button
        variant="ghost"
        size="sm"
        color="gray.600"
        _hover={{ bg: 'gray.100' }}
        onClick={() => {
          console.log('Currency selector clicked, current:', currency)
          // Toggle between USD and IDR
          const newCurrency = currency === 'USD' ? 'IDR' : 'USD'
          console.log('Switching to:', newCurrency)
          setCurrency(newCurrency)
        }}
      >
        <HStack gap={1}>
          <Text fontSize="sm">
            {CURRENCY_OPTIONS.find(opt => opt.value === currency)?.flag}
          </Text>
          <Text fontSize="sm" fontWeight="medium">
            {getCurrencyLabel()}
          </Text>
        </HStack>
      </Button>

      <Separator orientation="vertical" h={6} />

      {/* Language Selector - Simple Button for now */}
      <Button
        variant="ghost"
        size="sm"
        color="gray.600"
        _hover={{ bg: 'gray.100' }}
        onClick={() => {
          // Toggle between en and id
          setLanguage(language === 'en' ? 'id' : 'en')
        }}
      >
        <HStack gap={1}>
          <Text fontSize="sm">
            {LANGUAGE_OPTIONS.find(opt => opt.value === language)?.flag}
          </Text>
          <Text fontSize="sm" fontWeight="medium">
            {language.toUpperCase()}
          </Text>
        </HStack>
      </Button>
    </HStack>
  )
}

export default CurrencyLanguageSelector
