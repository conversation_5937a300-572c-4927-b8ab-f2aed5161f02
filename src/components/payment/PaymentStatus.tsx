'use client'
import React from 'react'
import {
  Box,
  Card,
  Heading,
  Text,
  VStack,
  HStack,
  <PERSON>ge,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react'
import { usePaymentStatusQuery, openPaymentUrl, formatCurrency } from '@/services/usePaymentQuery'
import { useTranslations } from 'next-intl'

interface PaymentStatusProps {
  orderId: string
  orderNumber: string
  totalAmount: number
  currency: 'USD' | 'IDR'
  onPaymentSuccess?: () => void
}

const PaymentStatus: React.FC<PaymentStatusProps> = ({
  orderId,
  orderNumber,
  totalAmount,
  currency,
  onPaymentSuccess
}) => {
  const t = useTranslations()
  const { data: paymentStatus, isLoading, error, refetch } = usePaymentStatusQuery(orderId)

  // Auto-refresh when payment is successful
  React.useEffect(() => {
    if (paymentStatus?.status === 'PAID' && onPaymentSuccess) {
      onPaymentSuccess()
    }
  }, [paymentStatus?.status, onPaymentSuccess])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'green'
      case 'PENDING':
        return 'yellow'
      case 'FAILED':
        return 'red'
      case 'EXPIRED':
        return 'gray'
      case 'CANCELLED':
        return 'red'
      default:
        return 'gray'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'Payment Successful'
      case 'PENDING':
        return 'Waiting for Payment'
      case 'FAILED':
        return 'Payment Failed'
      case 'EXPIRED':
        return 'Payment Expired'
      case 'CANCELLED':
        return 'Payment Cancelled'
      default:
        return 'Unknown Status'
    }
  }

  const handleRetryPayment = async () => {
    // Create new payment invoice for retry
    try {
      // This would typically call the create invoice API again
      // For now, just refresh the status
      refetch()
    } catch (error) {
      console.error('Failed to retry payment:', error)
    }
  }

  if (isLoading) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">Payment Status</Heading>
        </Card.Header>
        <Card.Body>
          <HStack>
            <Spinner size="sm" />
            <Text>Loading payment status...</Text>
          </HStack>
        </Card.Body>
      </Card.Root>
    )
  }

  if (error) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">Payment Status</Heading>
        </Card.Header>
        <Card.Body>
          <Alert status="error">
            <AlertIcon />
            <AlertDescription>
              Failed to load payment status. Please try again.
            </AlertDescription>
          </Alert>
          <Button mt={4} onClick={() => refetch()}>
            Retry
          </Button>
        </Card.Body>
      </Card.Root>
    )
  }

  if (!paymentStatus) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">Payment Status</Heading>
        </Card.Header>
        <Card.Body>
          <Text>No payment information found for this order.</Text>
        </Card.Body>
      </Card.Root>
    )
  }

  return (
    <Card.Root>
      <Card.Header>
        <Heading size="md">Payment Information</Heading>
      </Card.Header>
      <Card.Body>
        <VStack align="stretch" gap={4}>
          {/* Payment Status */}
          <HStack justify="space-between">
            <Text fontWeight="medium">Status:</Text>
            <Badge colorScheme={getStatusColor(paymentStatus.status)} size="lg">
              {getStatusText(paymentStatus.status)}
            </Badge>
          </HStack>

          {/* Amount */}
          <HStack justify="space-between">
            <Text fontWeight="medium">Amount:</Text>
            <Text fontWeight="bold" fontSize="lg">
              {formatCurrency(paymentStatus.amount, paymentStatus.currency as 'USD' | 'IDR')}
            </Text>
          </HStack>

          {/* Payment Method */}
          {paymentStatus.paymentMethod && (
            <HStack justify="space-between">
              <Text fontWeight="medium">Payment Method:</Text>
              <Text>{paymentStatus.paymentMethod}</Text>
            </HStack>
          )}

          {/* Payment Channel */}
          {paymentStatus.paymentChannel && (
            <HStack justify="space-between">
              <Text fontWeight="medium">Payment Channel:</Text>
              <Text>{paymentStatus.paymentChannel}</Text>
            </HStack>
          )}

          {/* Paid At */}
          {paymentStatus.paidAt && (
            <HStack justify="space-between">
              <Text fontWeight="medium">Paid At:</Text>
              <Text>{new Date(paymentStatus.paidAt).toLocaleString()}</Text>
            </HStack>
          )}

          {/* Created At */}
          <HStack justify="space-between">
            <Text fontWeight="medium">Created:</Text>
            <Text>{new Date(paymentStatus.createdAt).toLocaleString()}</Text>
          </HStack>

          {/* Status-specific content */}
          {paymentStatus.status === 'PENDING' && (
            <Alert status="info">
              <AlertIcon />
              <VStack align="start" flex={1}>
                <AlertDescription>
                  Your payment is being processed. This may take a few minutes.
                </AlertDescription>
                <Text fontSize="sm" color="gray.600">
                  You will receive a confirmation once the payment is completed.
                </Text>
              </VStack>
            </Alert>
          )}

          {paymentStatus.status === 'FAILED' && (
            <Alert status="error">
              <AlertIcon />
              <VStack align="start" flex={1}>
                <AlertDescription>
                  Payment failed. Please try again or use a different payment method.
                </AlertDescription>
                <Button size="sm" colorScheme="red" mt={2} onClick={handleRetryPayment}>
                  Retry Payment
                </Button>
              </VStack>
            </Alert>
          )}

          {paymentStatus.status === 'EXPIRED' && (
            <Alert status="warning">
              <AlertIcon />
              <VStack align="start" flex={1}>
                <AlertDescription>
                  Payment link has expired. Please create a new payment.
                </AlertDescription>
                <Button size="sm" colorScheme="orange" mt={2} onClick={handleRetryPayment}>
                  Create New Payment
                </Button>
              </VStack>
            </Alert>
          )}

          {paymentStatus.status === 'PAID' && (
            <Alert status="success">
              <AlertIcon />
              <AlertDescription>
                Payment completed successfully! Your order is being processed.
              </AlertDescription>
            </Alert>
          )}

          {/* Real-time status indicator for pending payments */}
          {paymentStatus.status === 'PENDING' && (
            <Box textAlign="center" py={2}>
              <HStack justify="center">
                <Spinner size="xs" />
                <Text fontSize="sm" color="gray.600">
                  Checking payment status...
                </Text>
              </HStack>
            </Box>
          )}
        </VStack>
      </Card.Body>
    </Card.Root>
  )
}

export default PaymentStatus
