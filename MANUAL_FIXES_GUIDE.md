# 🔧 Manual Fixes Guide

## 1. Database Migration - Add Starting Bid

Jalankan SQL berikut di database:

```sql
-- Add startingBid field to AutoBid table
ALTER TABLE `AutoBid` ADD COLUMN `startingBid` DECIMAL(10,2) NOT NULL DEFAULT 0.00;

-- Update existing records to have a reasonable starting bid
UPDATE `AutoBid` ab
JOIN `Product` p ON ab.productId = p.id
SET ab.startingBid = COALESCE(p.currentBid, p.priceUSD) + ab.bidIncrement
WHERE ab.startingBid = 0.00;
```

## 2. Fix Currency Rate Unique Constraint

Jika ada error unique constraint untuk CurrencyRate, jalankan:

```sql
-- Check existing rates
SELECT * FROM ExchangeRate WHERE fromCurrency = 'USD' AND toCurrency = 'IDR' ORDER BY createdAt DESC LIMIT 5;

-- Delete duplicate rates if any (keep the latest)
DELETE er1 FROM ExchangeRate er1
INNER JOIN ExchangeRate er2 
WHERE er1.id < er2.id 
AND er1.fromCurrency = er2.fromCurrency 
AND er1.toCurrency = er2.toCurrency 
AND DATE(er1.createdAt) = DATE(er2.createdAt);
```

## 3. Test Currency Conversion

Setelah migration, test currency conversion:

1. **Open Browser Console**
2. **Go to Product Page**
3. **Check Logs**:
   - Should see: "Converting X from USD to IDR"
   - Should see: "ProductCard: [product] - Original: X USD, Converted: Y IDR"

## 4. Test Auto-bid Starting Bid

1. **Go to Auction Product**
2. **Enable Auto-bid**
3. **Check Starting Bid Options**:
   - Should show same options as "Bid Now"
   - Should filter options > current bid
   - Should auto-adjust max budget if needed

## 5. Verify Exchange Rate

Check if exchange rate is fetched correctly:

```bash
curl -X GET http://localhost:3000/currency/rate
```

Expected response:
```json
{
  "status": true,
  "data": {
    "rate": 15539.25,
    "lastUpdated": "2024-01-20T10:00:00.000Z",
    "source": "database"
  }
}
```

## 6. Debug Currency Issues

If currency conversion still not working:

1. **Check Console Logs**:
   - Look for "Converting X from USD to IDR"
   - Check if rate is loaded: "Exchange rate fetched: X"

2. **Check Currency Service**:
   ```javascript
   // In browser console
   console.log(window.__CURRENCY_SERVICE__)
   ```

3. **Check Currency Context**:
   ```javascript
   // In browser console
   console.log(window.__CURRENCY_CONTEXT__)
   ```

## 7. Common Issues & Solutions

### Issue: Prices still in USD
**Solution**: Check if currency service is loaded and rate is fetched

### Issue: Auto-bid starting bid not showing options
**Solution**: Ensure bidOptions is available and filtered correctly

### Issue: Currency switching not working
**Solution**: Check if invalidateQueries is called properly

### Issue: Database connection error
**Solution**: Check database credentials and connection

## 8. Testing Checklist

- [ ] Database migration completed
- [ ] Exchange rate fetched from API
- [ ] Currency conversion working (USD ↔ IDR)
- [ ] Product prices display correctly
- [ ] Currency switching updates all prices
- [ ] Auto-bid starting bid shows options
- [ ] Auto-bid validation works
- [ ] Bid history loads correctly

## 9. Production Deployment

1. **Run Migration**: Execute SQL in production database
2. **Test API**: Verify currency API endpoints work
3. **Monitor Logs**: Check for any errors in currency conversion
4. **User Testing**: Test currency switching and auto-bid features
