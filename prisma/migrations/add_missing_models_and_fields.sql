-- Add missing fields and models to sync with schema

-- Add bidType field to Bid table if it doesn't exist
ALTER TABLE `Bid` ADD COLUMN `bidType` VARCHAR(191) NOT NULL DEFAULT 'manual';

-- Add currency field to Order table if it doesn't exist
ALTER TABLE `Order` ADD COLUMN `currency` VARCHAR(191) NOT NULL DEFAULT 'USD';

-- Add currency field to OrderItem table if it doesn't exist
ALTER TABLE `OrderItem` ADD COLUMN `currency` VARCHAR(191) NOT NULL DEFAULT 'USD';

-- Create AutoBid table if it doesn't exist
CREATE TABLE `AutoBid` (
    `id` VARCHAR(191) NOT NULL,
    `productId` VARCHAR(191) NOT NULL,
    `bidderId` VARCHAR(191) NOT NULL,
    `startingBid` DECIMAL(10, 2) NOT NULL,
    `maxBudget` DECIMAL(10, 2) NOT NULL,
    `bidIncrement` DECIMAL(10, 2) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `AutoBid_productId_bidderId_key`(`productId`, `bidderId`),
    INDEX `AutoBid_productId_idx`(`productId`),
    INDEX `AutoBid_bidderId_idx`(`bidderId`),
    INDEX `AutoBid_isActive_idx`(`isActive`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create OrderStatusHistory table if it doesn't exist
CREATE TABLE `OrderStatusHistory` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `createdBy` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `OrderStatusHistory_orderId_idx`(`orderId`),
    INDEX `OrderStatusHistory_status_idx`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create CurrencyRate table if it doesn't exist
CREATE TABLE `CurrencyRate` (
    `id` VARCHAR(191) NOT NULL,
    `fromCurrency` VARCHAR(191) NOT NULL,
    `toCurrency` VARCHAR(191) NOT NULL,
    `rate` DECIMAL(15, 6) NOT NULL,
    `sellRate` DECIMAL(15, 6) NOT NULL,
    `buyRate` DECIMAL(15, 6) NOT NULL,
    `margin` DECIMAL(5, 4) NOT NULL DEFAULT 0.02,
    `source` VARCHAR(191) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `date` DATE NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `CurrencyRate_fromCurrency_toCurrency_date_key`(`fromCurrency`, `toCurrency`, `date`),
    INDEX `CurrencyRate_date_idx`(`date`),
    INDEX `CurrencyRate_isActive_idx`(`isActive`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create Payment table if it doesn't exist
CREATE TABLE `Payment` (
    `id` VARCHAR(191) NOT NULL,
    `orderId` VARCHAR(191) NOT NULL,
    `externalId` VARCHAR(191) NOT NULL,
    `xenditInvoiceId` VARCHAR(191) NULL,
    `xenditPaymentId` VARCHAR(191) NULL,
    `amount` DECIMAL(10, 2) NOT NULL,
    `currency` VARCHAR(191) NOT NULL DEFAULT 'IDR',
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `invoiceUrl` TEXT NULL,
    `expiryDate` DATETIME(3) NULL,
    `expiredAt` DATETIME(3) NULL,
    `paidAt` DATETIME(3) NULL,
    `paidAmount` DECIMAL(10, 2) NULL,
    `paymentMethod` VARCHAR(191) NULL,
    `paymentChannel` VARCHAR(191) NULL,
    `webhookData` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `Payment_orderId_key`(`orderId`),
    UNIQUE INDEX `Payment_externalId_key`(`externalId`),
    UNIQUE INDEX `Payment_xenditInvoiceId_key`(`xenditInvoiceId`),
    UNIQUE INDEX `Payment_xenditPaymentId_key`(`xenditPaymentId`),
    INDEX `Payment_orderId_idx`(`orderId`),
    INDEX `Payment_status_idx`(`status`),
    INDEX `Payment_xenditInvoiceId_idx`(`xenditInvoiceId`),
    INDEX `Payment_xenditPaymentId_idx`(`xenditPaymentId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create PaymentMethod table if it doesn't exist
CREATE TABLE `PaymentMethod` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `type` VARCHAR(191) NOT NULL,
    `currency` VARCHAR(191) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `isRecommended` BOOLEAN NOT NULL DEFAULT false,
    `icon` VARCHAR(191) NULL,
    `processingFee` DECIMAL(10, 2) NOT NULL,
    `processingFeeType` VARCHAR(191) NOT NULL,
    `minAmount` DECIMAL(10, 2) NOT NULL,
    `maxAmount` DECIMAL(15, 2) NOT NULL,
    `supportedCountries` JSON NOT NULL,
    `features` JSON NOT NULL,
    `xenditConfig` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `PaymentMethod_type_idx`(`type`),
    INDEX `PaymentMethod_currency_idx`(`currency`),
    INDEX `PaymentMethod_isActive_idx`(`isActive`),
    UNIQUE INDEX `PaymentMethod_name_currency_key`(`name`, `currency`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create ExchangeRate table if it doesn't exist
CREATE TABLE `ExchangeRate` (
    `id` VARCHAR(191) NOT NULL,
    `fromCurrency` VARCHAR(191) NOT NULL,
    `toCurrency` VARCHAR(191) NOT NULL,
    `rate` DECIMAL(20, 8) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ExchangeRate_fromCurrency_toCurrency_key`(`fromCurrency`, `toCurrency`),
    INDEX `ExchangeRate_fromCurrency_toCurrency_idx`(`fromCurrency`, `toCurrency`),
    INDEX `ExchangeRate_updatedAt_idx`(`updatedAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add foreign key constraints for AutoBid
ALTER TABLE `AutoBid` ADD CONSTRAINT `AutoBid_bidderId_fkey` FOREIGN KEY (`bidderId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `AutoBid` ADD CONSTRAINT `AutoBid_productId_fkey` FOREIGN KEY (`productId`) REFERENCES `Product`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add foreign key constraints for OrderStatusHistory
ALTER TABLE `OrderStatusHistory` ADD CONSTRAINT `OrderStatusHistory_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- Add foreign key constraints for Payment
ALTER TABLE `Payment` ADD CONSTRAINT `Payment_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
