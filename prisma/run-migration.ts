import { PrismaClient } from '../generated/client'
import fs from 'fs'
import path from 'path'

const prisma = new PrismaClient()

async function runMigration() {
  try {
    console.log('🔧 Running manual migration...')
    
    const migrationPath = path.join(__dirname, 'migrations', 'add_missing_models_and_fields.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Split SQL by semicolons and execute each statement
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`Executing: ${statement.substring(0, 50)}...`)
        try {
          await prisma.$executeRawUnsafe(statement)
          console.log('✅ Success')
        } catch (error: any) {
          if (error.message.includes('already exists') || error.message.includes('Duplicate column')) {
            console.log('⚠️ Already exists, skipping')
          } else {
            console.error('❌ Error:', error.message)
          }
        }
      }
    }
    
    console.log('🎉 Migration completed!')
  } catch (error) {
    console.error('❌ Migration failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

runMigration()
